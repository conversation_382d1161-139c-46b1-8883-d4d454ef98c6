name: Process wrapper PR

on:
  pull_request:
    types: [opened, ready_for_review]

jobs:
  call-shared-workflow:
    uses: Sababuu/shared-workflows/.github/workflows/PR-ready-for-review.yml@main
    secrets:
      AWS_S3_PR_ACCESS_KEY: ${{ secrets.AWS_S3_PR_ACCESS_KEY }}
      AWS_S3_PR_SECRET_KEY: ${{ secrets.AWS_S3_PR_SECRET_KEY }}
      S3_PR_IMAGES_BUCKET_NAME: ${{ secrets.S3_PR_IMAGES_BUCKET_NAME }}
      LINEAR_API_TOKEN_CG: ${{ secrets.LINEAR_API_TOKEN_CG }}
      LINEAR_API_TOKEN_CK: ${{ secrets.LINEAR_API_TOKEN_CK }}
      LINEAR_API_TOKEN_JB: ${{ secrets.LINEAR_API_TOKEN_JB }}
      LINEAR_API_TOKEN_DR: ${{ secrets.LINEAR_API_TOKEN_DR }}
      PR_READY_FOR_REVIEW_SLACK_HOOK: ${{ secrets.PR_READY_FOR_REVIEW_SLACK_HOOK }}
