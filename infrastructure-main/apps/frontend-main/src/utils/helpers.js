import axios from "axios";
import moment from "moment";
import config from "@/config";
import { Cloudinary } from "@cloudinary/url-gen";

const cld = new Cloudinary({
  cloud: { cloudName: config.cloudinary.cloudName },
});

export const IS_CLOUDINARY_URL = /res.cloudinary.com/;

export const IS_DEFAULT_DESCRIPTION =
  /^Hey.*I'm excited to meet new people!.*$/i;

export function isVodacomTanzaniaNumber(phone) {
  phone = phone.replace(" ", "");
  phone = phone.replace(/[\s-]+/g, "");
  const pattern = /^\+255(74|75|76)\d{7}$/;
  return pattern.test(phone);
}

export function getCountryFromIp() {
  const url = "https://ip2c.org/s";

  return axios
    .get(url)
    .then((response) => {
      const result = response.data;
      // Data is expected in this format: '1;UG;UGA;Uganda'
      // therefore we split it by ';' and get the second element [1]
      return result.split(";")[1];
    })
    .catch((err) => {
      console.error(err);
      return null;
    });
}

export async function getCurrentLocation(locationCallback, permissionCallback) {
  const permissionStatus = await checkLocationPermission();

  if (permissionStatus === "granted") {
    // Permission already granted
    requestLocation(locationCallback, permissionCallback, true);
  } else if (permissionStatus === "denied") {
    // Permission already denied
    if (permissionCallback) {
      permissionCallback("denied");
    }
    if (locationCallback) {
      locationCallback(undefined, false);
    }
  } else {
    // Permission prompt or unknown status
    requestLocation(locationCallback, permissionCallback, false);
  }
}

function requestLocation(
  locationCallback,
  permissionCallback,
  isAlreadyGranted
) {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;

        const locationDetails = {
          longitude,
          latitude,
        };

        if (locationCallback) {
          locationCallback(locationDetails, true);
        }

        // If permission was prompted and granted
        if (!isAlreadyGranted && permissionCallback) {
          permissionCallback("granted");
        }
      },
      (error) => {
        console.error("Geolocation error:", error);
        if (permissionCallback) {
          permissionCallback("denied");
        }
        if (locationCallback) {
          locationCallback(undefined, false);
        }
      },
      {
        enableHighAccuracy: true,
      }
    );
  } else {
    console.error("Geolocation is not supported by this browser.");
  }
}

export const isUserNetworkActive = () => {
  return navigator.onLine;
};

export const getDisplayMode = () => {
  let displayMode = "browser";

  const mqStandAlone = "(display-mode: standalone)";

  if (navigator.standalone || window.matchMedia(mqStandAlone).matches) {
    displayMode = "standalone";
  }

  if (window.SababuuNativeBridge) {
    displayMode = "app";
  }

  return displayMode;
};

export async function checkLocationPermission() {
  if (navigator.permissions) {
    try {
      const permissionStatus = await navigator.permissions.query({
        name: "geolocation",
      });
      return permissionStatus.state; // 'granted', 'prompt', or 'denied'
    } catch (error) {
      console.error("Error checking geolocation permission:", error);
      return null;
    }
  } else {
    // Permissions API not supported, fallback to directly requesting geolocation
    return null;
  }
}

export const scrollToTop = (elIdentifier = "#scroltop-el") => {
  const el = document.querySelector(elIdentifier);
  if (el) el.scrollIntoView();
};

export const getResized = (item, fileName) => {
  return new Promise((resolve) => {
    let max_width = 600;
    let max_height = 800;

    let reader = new FileReader();

    reader.readAsDataURL(item);
    reader.name = item.name;
    reader.size = item.size;
    reader.onload = function (event) {
      let img = new Image();
      img.src = event.target.result;
      img.size = event.target.size;
      img.onload = function (el) {
        let elem = document.createElement("canvas");

        if (el.target.width >= el.target.height) {
          if (el.target.width > max_width) {
            let scaleFactor = max_width / el.target.width;
            elem.width = max_width;
            elem.height = el.target.height * scaleFactor;
          }
        } else {
          if (el.target.height >= max_height) {
            let scaleFactor = max_height / el.target.height;
            elem.height = max_height;
            elem.width = el.target.width * scaleFactor;
          } else {
            resolve(item);
          }
        }

        let ctx = elem.getContext("2d");
        ctx.drawImage(el.target, 0, 0, elem.width, elem.height);

        const resized = ctx.canvas.toDataURL("image/png", 1);
        fetch(resized)
          .then((response) => response.blob())
          .then((blob) => {
            const file = new File([blob], fileName, { type: blob.type });
            resolve(file);
          });
      };
    };
  });
};

export function isOlderThanDays(date, days) {
  return moment.utc().diff(moment.utc(date, false), "days") > days;
}

export function getUtmParametersFromLocalStorage() {
  return {
    utm_source: localStorage.getItem("utm_source"),
    utm_medium: localStorage.getItem("utm_medium"),
    utm_campaign: localStorage.getItem("utm_campaign"),
  };
}

export function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
}

export const checkCloudinaryModerationError = (result) => {
  let awsRekStatus = null;
  let moderation = result.info?.moderation
    ? result.info?.moderation
    : result.moderation;
  if (moderation) {
    const awsRek = moderation.find((m) => m.kind === "aws_rek");
    awsRekStatus = awsRek.status;
  }

  return {
    moderationError:
      awsRekStatus === "rejected" ? extractModerationError(moderation) : null,
  };
};

const extractModerationError = (modInfo, verbose = false) => {
  let errorMsg = "Image validation failed: ";
  const errorSuffix = ". Try a different image.";

  if (Array.isArray(modInfo)) {
    for (const info of modInfo) {
      if (info?.response.moderation_labels) {
        let i = 0;
        for (const label of info.response.moderation_labels) {
          if (label.name) {
            errorMsg += label.name;
          } else if (label.parent_name) {
            errorMsg += label.parent_name;
          }

          if (!verbose) {
            return errorMsg + errorSuffix;
          } else {
            if (i < info.response.moderation_labels.length - 1) {
              errorMsg += ", ";
            }
          }
          i++;
        }
      }
    }
  }
  return errorMsg + errorSuffix;
};

const PUBLIC_ID_REGEX = /\/upload\/(.*)\/(sababuu_.*).webp$/;

export const getCloudinaryUrl = (srcUrl) => {
  const matches = srcUrl.match(PUBLIC_ID_REGEX);
  if (matches) {
    /*
     * We don't have transformation set on the back-end
     * for full images so we ignore it for now
     */
    // const transformation = matches[1];
    const publicId = matches[2];

    const path = cld.image(publicId).addTransformation("c_crop,g_custom");
    return path;
  }
  return null;
};

export const getCloudinaryAvatarUrl = (srcUrl) => {
  const matches = srcUrl.match(PUBLIC_ID_REGEX);
  if (matches) {
    const transformation = matches[1];
    const publicId = matches[2];
    const path = cld
      .image(publicId)
      .addTransformation(transformation.includes(",") ? transformation : "");
    return path;
  }
  return null;
};

export const stripHtml = (str) => {
  const tmp = document.createElement("div");
  tmp.innerHTML = str;
  return tmp.textContent || tmp.innerText || "";
};
export const formatHeightValue = (cm) => {
  if (!cm) return "";

  const totalInches = cm / 2.54;
  const feet = Math.floor(totalInches / 12);
  const inches = Math.round(totalInches % 12);

  if (inches === 12) {
    return `${cm}cm (${feet + 1}ft 0inch)`;
  }

  return `${cm}cm (${feet}ft ${inches} ${inches === 1 ? "inch" : "inches"})`;
};

export const pickNUniqueRandom = (arr, n) => {
  if (!arr || arr.length === 0 || n <= 0) {
    return [];
  }
  const count = Math.min(n, arr.length);

  const shuffled = [...arr]; // Create a copy
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }

  return shuffled.slice(0, count);
};

export function addDays(date, days) {
  let result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export const checkIfExceedsClientHeight = (someElement) => {
  if (!someElement || !someElement.scrollHeight) return false;
  const wrapperContent = document.querySelector("#wrapper-content");
  if (!wrapperContent) return false;
  return someElement.scrollHeight > wrapperContent.clientHeight;
};
