
export const messages = {
  vodacomMomoNotAvailable:
    "Sorry, Vodacom Mpesa isn't live yet. We'll notify you once it's available.",
};

export const MATCH_CHECK_INTERVAL = 3000; // 3s

export const HEART_BEAT_INTERVAL_MILISECONDS = 60 * 1000; // 1m

export const COUNTRY_DATA_TTL = 10 * 60 * 1000; // 10m

export const PROFILES_TTL = 10 * 60 * 1000; // 10m

export const FLOW_CARD_PUSH_TTL = 6 * 60 * 60 * 1000; // 6h

export const ACTION_ALREADY_EXIST_STATUS_CODE = 409;

export const MIN_PROFILE_CARDS_BEFORE_INJECTION = Number(
  import.meta.env.VITE_MIN_PROFILE_CARDS_BEFORE_INJECTION ?? 4
);
export const MAX_PROFILE_CARDS_BEFORE_INJECTION = Number(
  import.meta.env.VITE_MAX_PROFILE_CARDS_BEFORE_INJECTION ?? 8
);

export const SURVEY_TYPES = {
  SINGLE: "single_choice",
  MULTI: "multiple_choice",
  RATING: "rating",
  OPEN: "open",
};

export const MAX_INTERESTS_ALLOWED = 12;

// Actionable types for likes
export const ACTIONABLE_TYPES = {
  USER: "User",
  IMAGE: "Image",
  USER_PROPERTY: "UserProperty",
};

// Property types for display in feed cards
export const PROPERTY_TYPES = {
  PROFILE: "profile",
  DESCRIPTION: "description",
  PHOTO: "photo",
  PROPERTY: "property",
};

// Action types for feed entries
export const ACTION_TYPES = {
  LIKE: "like",
  GIFT: "gift",
};
