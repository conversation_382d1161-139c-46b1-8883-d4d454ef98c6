<template>
  <div id="btn-instachat" class="wrapper">
    <div v-if="!profile.is_boosted">
      <h2>Want to chat immediately?</h2>
      <p>Send this person a message before getting a match.</p>
    </div>
    <div v-if="profile.is_boosted">
      <h2>Instachat for free</h2>
      <p>
        Send this person a message and get
        {{ boostPackage?.reward_value }} free credit!
      </p>
    </div>
    <div class="instachat" @click="instachat()">Instachat</div>
    <BoostPassButton v-if="profile.is_boosted" :profile="profile" />
  </div>
</template>
<script setup>
import bus from "@/bus";
import { computed, inject } from "vue";

import { useProfilesStore } from "@/stores/profiles";
import { isUserNetworkActive } from "@/utils/helpers.js";

import BoostPassButton from "@/components/buttons/BoostPassButton.vue";

const profilesStore = useProfilesStore();

const props = defineProps({
  profile: { type: Object, required: true },
});

const boostPackage = computed(() => {
  const boosts = props.profile?.profile_boosts;
  if (!boosts || boosts.length < 1) return null;

  return boosts[0].boost_package;
});

const confirm = inject("action.confirm");

const instachat = () => {
  if (!isUserNetworkActive()) {
    bus.global.emit("notification.top:show", {
      type: "error",
      message: "You are offline",
    });
    return;
  }

  if (props.profile.is_boosted) {
    profilesStore.setBoostedProfile(props.profile);
    bus.global.emit("modal.overlay:open", {
      name: "boosted-instachat",
    });
    return;
  }

  confirm("instachat.init.dialog", {
    userId: props.profile.id,
    is_boosted_profile: props.profile.is_boosted,
    verified: props.profile.person_verification,
    number_of_images: 1 + props.profile.gallery.length,
    description_length: props.profile.description.length,
    profile_actionable: props.profile.actionable || false,
  })
    .then(() => {
      profilesStore.removeProfile(props.profile.id);
    })
    .catch((error) => {
      if (error) {
        bus.global.emit("notification.top:show", {
          type: "error",
          message:
            "Something went wrong initializing instachat with this profile",
        });
      }
    });
};
</script>
<style lang="scss" scoped>
.wrapper {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  row-gap: 1rem;
  transition: all 0.5s;
  box-shadow: $profile-card-item-shadow-sm;
  background-color: white;
  border: 1px solid #ebebeb;
  border-radius: $app-card-border-radius;
  padding: 16px;

  h2 {
    font-size: 14px;
    font-weight: bolder;
    margin-bottom: 8px;
    .star {
      width: 20px;
      height: 20px;
      display: inline-flex;
      justify-content: center;
      vertical-align: middle;
      border-radius: 50%;
      background: linear-gradient(91.47deg, #e94057 0%, #8a2387 98.75%);
      svg {
        width: 70%;
      }
    }
  }

  .instachat {
    border: none;
    box-shadow: none;
    background: linear-gradient(91.47deg, #e94057 0%, #8a2387 98.75%);
    border-radius: 5px;
    height: 56px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
    &.disabled {
      filter: grayscale(95%) blur(1px);
      opacity: 0.8;
    }

    &:active {
      box-shadow: 0 20px 50px rgba(138, 35, 135, 1) !important;
    }

    & > svg {
      width: 60%;
    }
  }
}
.free-message {
  text-align: center;
  font-weight: bold;
  margin-bottom: 10px;
}
</style>
