<template>
  <div class="confirmation">
    <div
      v-if="data.initLoading || !giftsStore.activeProfile"
      class="confirmation__loader"
    >
      <PreloaderSpinner dark />
    </div>

    <div v-else class="confirmation__body">
      <div class="confirmation__body__title">
        Send a gift
        <div class="gift__body__balance">
          <span>Current balance</span>
          <div>
            <CreditsIcon class="credits-icon-small" />
            <span>
              {{ balanceStore.getBalance }}
              {{ balanceStore.getBalance === 1 ? "credit" : "credits" }}
            </span>
          </div>
        </div>
      </div>
      <div class="confirmation__body__gifts">
        <div
          v-for="(gift, idx) in giftsStore.gifts"
          :key="`gift-${idx}`"
          class="confirmation__body__gifts__item"
          :class="{
            active: data.selectedGift?.id === gift.id,
          }"
          @click="() => setGift(gift)"
        >
          <div class="confirmation__body__gifts__item__image">
            <img :src="gift.image" />
          </div>
          <div class="confirmation__body__gifts__item__details">
            <span>{{ gift.name }}</span>
            <span>{{ gift.cost }} credits</span>
          </div>
        </div>
      </div>
      <div v-if="hasEnoughFunds" class="confirmation__body__actions">
        <p class="text__prompt">
          Select your gift and surprise
          <span>{{ giftsStore.activeProfile.name }}!</span>
        </p>
        <ButtonMain
          :disabled="data.selectedGift === null || data.buyingGift === true"
          :loading="data.buyingGift"
          @click="buyGift"
          >Send</ButtonMain
        >
      </div>

      <div v-if="!hasEnoughFunds" class="confirmation__body__funds">
        <p class="text__prompt">
          You need a bit more credits to send a gift. Top up now and surprise
          <span>{{ giftsStore.activeProfile.name }}!</span>
        </p>
        <ModuleTopUp
          :opened-from="'gifts'"
          :required-credits="giftsStore.gifts[0]?.cost || 5"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive } from "vue";
import bus from "@/bus";

import { useProfileStore } from "@/stores/profile";
import { useBalanceStore } from "@/stores/balance";
import { useGiftsStore } from "@/stores/gifts";

import ButtonMain from "@/components/buttons/ButtonMain.vue";
import ModuleTopUp from "@/components/ModuleTopUp.vue";
import PreloaderSpinner from "@/components/PreloaderSpinner.vue";

import CreditsIcon from "@/assets/icons/credits.svg";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";
import { computed } from "vue";

const profileStore = useProfileStore();
const balanceStore = useBalanceStore();
const giftsStore = useGiftsStore();

const data = reactive({
  initLoading: true,
  selectedGift: null,
  buyingGift: false,
  hasEnoughFunds: false,
});

const hasEnoughFunds = computed(() =>
  giftsStore.gifts.some(({ cost }) => cost <= balanceStore.balance)
);

const setGift = (gift) => {
  if (!hasEnoughFunds.value || balanceStore.balance < gift.cost) return;
  data.selectedGift = gift;
};

const buyGift = async () => {
  try {
    if (data.buyingGift) return;
    data.buyingGift = true;

    await giftsStore.buyGift({
      giftId: data.selectedGift.id,
      profileId: giftsStore.activeProfile.id,
      source: giftsStore.activeSource,
    });
    logEvent(trackedEvents.GIFT_PURCHASED, {
      source: giftsStore.activeSource,
      profileId: giftsStore.activeProfile.id,
      gift_name: data.selectedGift.name,
      gift_cost: data.selectedGift.cost,
    });
    bus.global.emit("notification.top:show", {
      type: "success",
      title: "Your gift has been sent",
      message: `${giftsStore.activeProfile.name} will receive your gift in a few seconds`,
      duration: 5000,
    });
    bus.global.emit("gift-bought", { gift: data.selectedGift });
  } catch (ex) {
    console.error(ex);
    bus.global.emit("notification.top:show", {
      type: "error",
      title: "We can't send your gift",
      message: "Try a bit later",
      duration: 5000,
    });
  } finally {
    data.buyingGift = false;
  }
};

onMounted(() => {
  Promise.all([giftsStore.fetchGifts(), profileStore.fetchProfile()])
    .then(() => {
      logEvent(trackedEvents.GIFT_OPENED, {
        source: giftsStore.activeSource,
      });
    })
    .catch((ex) => {
      console.error(ex);
    })
    .finally(() => {
      data.initLoading = false;
    });
});
</script>

<style lang="scss">
.credits-icon-small {
  width: 16px;
  fill: #e94057;
  margin-right: 2px;
}

.confirmation {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 1.5rem;
  &__body__title {
    width: 100%;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
  }

  .gift__body__balance {
    display: flex;
    flex-direction: column;
    row-gap: 2px;

    & > span {
      font-size: 12px;
      font-weight: 700;
    }

    & > div {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: auto;
      column-gap: 5px;
      color: #000;

      & > svg {
        color: rgba(233, 64, 87, 1);
      }

      & > span {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }

  &__loader {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__body {
    width: 100%;
    &__gifts {
      display: flex;
      column-gap: 1rem;

      &__item {
        flex: 1;
        border: 2px solid;
        border-color: rgb(245, 245, 245);
        border-radius: 5px;

        &.active {
          border-color: $primary-color;
        }

        &__image {
          height: 4rem;
          width: 100%;
          background-color: rgb(250, 250, 250);
          display: flex;
          justify-content: center;
          align-items: center;

          & > img {
            height: 65%;
          }
        }

        &__details {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 5px 0px;
          row-gap: 2px;

          & span:nth-child(1) {
            font-size: 14px;
            font-weight: bold;
          }

          & span:nth-child(2) {
            font-size: 14px;
            font-weight: bold;
          }
        }
      }
    }

    &__actions {
      margin-top: 2rem;
    }

    &__funds {
      margin-top: 1.5rem;
    }

    .text__prompt {
      color: var(--text-primary-100000000, #000);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%; /* 21px */
      margin-bottom: 2rem;
      margin-bottom: 1.5rem;

      & > span {
        font-weight: bold;
      }
    }
  }
}
</style>
