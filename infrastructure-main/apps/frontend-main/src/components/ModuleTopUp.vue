<script setup>
import { onMounted, reactive, computed } from "vue";
import { storeToRefs } from "pinia";
import bus from "@/bus";

import InputMobileNumberInternational from "@/components/InputMobileNumberInternational.vue";
import InputField from "@/components/InputField.vue";
import ButtonMain from "@/components/buttons/ButtonMain.vue";
import RibbonStick from "@/components/common/RibbonStick.vue";

import { messages } from "@/constants/index.js";

import { topupBalance } from "@/api/repositories/payment";
import { useAuthStore } from "@/stores/auth";
import { useBalanceStore } from "@/stores/balance";
import { useCountryStore } from "@/stores/country";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";

import Form from "@/services/Form/Form.js";
import {
  stringRequired,
  phoneFormat,
  checkCountryCode,
} from "@/services/Form/rules.js";

const { isFromG<PERSON>, user, isVodacomTanzaniaUser } = storeToRefs(
  useAuthStore()
);
const countryStore = useCountryStore();

const props = defineProps({
  openedFrom: {
    type: String,
    required: true,
  },
  requiredCredits: {
    type: Number,
    default: 0,
  },
});
const balanceStore = useBalanceStore();

const data = reactive({
  formValues: {
    phone: "",
  },
  form: new Form({
    phone: [stringRequired, phoneFormat, checkCountryCode],
  }),
  selectedTariff: null,
  useAlternatePaymentPhone: false,
  alternatePaymentPhoneLoading: false,
});

const formattedPhone = computed(() => {
  return `+${data.formValues.phone.replaceAll(/[-+()\\s]/g, "")}`;
});

// Determine if a package should be disabled based on required credits
const isPackageDisabled = (tariff) => {
  // If no specific required credits, all packages are enabled
  if (!props.requiredCredits) return false;

  // Current balance + credits from this package should be enough for the action
  const currentBalance = balanceStore.getBalance || 0;
  return currentBalance + tariff.credits < props.requiredCredits;
};

onMounted(() => {
  logEvent(trackedEvents.TOP_UP_OPENED, {
    opened_from: props.openedFrom,
    credit_balance: balanceStore.getBalance,
  });
});

const showSuccessNotification = () => {
  bus.global.emit("notification.top:show", {
    type: "success",
    title: "We have sent a payment request",
    message: "Your account will be topped up after confirmation.",
    duration: 5000,
  });
};

const showErrorNotification = (message) => {
  bus.global.emit("notification.top:show", {
    type: "error",
    message,
  });
};

const refill = (tariff, index) => {
  const userPhoneCorrespondent = user.value.phone_correspondent || null;
  const supportedPaymentCorrespondents =
    countryStore.countryData.supportedPaymentCorrespondents || null;
  if (
    userPhoneCorrespondent &&
    supportedPaymentCorrespondents &&
    !supportedPaymentCorrespondents.includes(userPhoneCorrespondent)
  ) {
    data.useAlternatePaymentPhone = true;
    data.selectedTariff = tariff;
    logEvent(trackedEvents.TOP_UP_ALTERNATE_OPENED);
    return;
  }

  if (isVodacomTanzaniaUser.value) {
    bus.global.emit("notification.top:show", {
      type: "error",
      message: messages.vodacomMomoNotAvailable,
    });
    return;
  }
  countryStore.countryData.tariffs[index].loading = true;

  topupBalance({ tariff: tariff.tariffId })
    .then(() => {
      logEvent(trackedEvents.TOP_UP_ATTEMPTED, {
        requested_amount: Number.parseInt(tariff.price),
        requested_currency: countryStore.countryData.currency,
        requested_credits: Number.parseInt(tariff.amount),
        credit_balance: balanceStore.getBalance,
      });
      showSuccessNotification();
    })
    .catch((error) => {
      logEvent(trackedEvents.TOP_UP_FAILED, {
        error: error?.data?.response?.data?.error || error,
      });
      bus.global.emit("notification.top:show", {
        type: "error",
        message:
          error?.data?.response?.data?.error ||
          "Something went wrong, try again later",
      });
    })
    .finally(() => {
      countryStore.countryData.tariffs[index].loading = false;
    });
};

const refillAlternate = () => {
  if (data.alternatePaymentPhoneLoading === true) return;
  data.alternatePaymentPhoneLoading = true;

  topupBalance({
    tariff: data.selectedTariff.tariffId,
    alternate_phone: formattedPhone.value,
  })
    .then(() => {
      logEvent(trackedEvents.TOP_UP_ALTERNATE_ATTEMPTED, {
        requested_amount: Number.parseInt(data.selectedTariff.amount),
        requested_currency: countryStore.countryData.currency,
        requested_credits: Number.parseInt(data.selectedTariff.credits),
        credit_balance: balanceStore.getBalance,
      });
      showSuccessNotification();
    })
    .catch((error) => {
      const errorMessage =
        error?.data?.response?.data?.error ||
        "Something went wrong, try again later";
      logEvent(trackedEvents.TOP_UP_ALTERNATE_FAILED, {
        error: errorMessage,
      });
      showErrorNotification(errorMessage);
    })
    .finally(() => {
      data.alternatePaymentPhoneLoading = false;
    });
};

const cancelRefillAlternate = () => {
  data.useAlternatePaymentPhone = false;
  data.selectedTariff = null;
  data.formValues.phone = "";
  logEvent(trackedEvents.TOP_UP_ALTERNATE_CANCELED);
};
</script>

<template>
  <div class="topup">
    <div
      v-if="data.useAlternatePaymentPhone === false"
      class="topup__package__wrapper"
    >
      <div
        v-for="(tariff, index) in countryStore.countryData?.tariffs"
        :key="index"
        class="topup__package"
        :class="{ 'topup__package--disabled': isPackageDisabled(tariff) }"
      >
        <div class="topup__package__tariff">
          <p class="topup__package__tariff__name">
            {{ tariff.credits }}
            {{ tariff.credits === 1 ? "Credit" : "Credits" }}
          </p>
          <span class="topup__package__tariff__nudge">{{
            tariff.description
          }}</span>
        </div>
        <div class="topup__package__action">
          <p class="topup__package__action__cost">
            {{ countryStore.countryData.currency }} {{ tariff.amount }}
          </p>
          <ButtonMain
            :loading="tariff.loading"
            :disabled="isFromGhana || isPackageDisabled(tariff)"
            styling="gradient"
            class="topup__package__action__button"
            @click="() => refill(tariff, index)"
          >
            Buy
          </ButtonMain>
          <RibbonStick v-if="isFromGhana" :text="'Back Soon'" class="ribbon" />
        </div>
      </div>
    </div>
    <div v-else class="topup__alternate__number">
      <!-- eslint-disable vue/no-v-html -->
      <p
        class="topup__alternate__number__prompt"
        v-html="countryStore.countryData.alternatePaymentPrompt"
      ></p>
      <!-- eslint-enable vue/no-v-html -->
      <InputField
        :errors="data.form.errors.get('phone')"
        class="topup__alternate__number__input__field"
      >
        <InputMobileNumberInternational
          v-model="data.formValues.phone"
          name="phone"
          placeholder=""
          type="number"
          :only-countries="[countryStore.countryData.alphaTwoCode]"
          :error="data.form.errors.has('phone')"
        />
      </InputField>
      <button-main
        :loading="data.alternatePaymentPhoneLoading"
        :disabled="
          data.alternatePaymentPhoneLoading || formattedPhone.length < 13
        "
        @click="refillAlternate"
      >
        Top Up {{ data.selectedTariff.amount }}
        {{ data.selectedTariff.credits === 1 ? "Credit" : "Credits" }} -
        {{ countryStore.countryData.currency }} {{ data.selectedTariff.amount }}
      </button-main>
      <button-main
        :disabled="data.alternatePaymentPhoneLoading"
        styling="secondary"
        @click="cancelRefillAlternate"
      >
        Cancel
      </button-main>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.topup {
  &__package__wrapper {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  &__package {
    display: flex;
    flex-direction: column;
    gap: 8px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border: 1px solid;
    background: #fff;
    border-radius: $app-card-border-radius;
    padding: 16px;
    border-color: #e5e7eb;
    transition: opacity 0.2s ease;

    &--disabled {
      opacity: 0.5;
    }

    &__tariff {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 8px;

      &__name {
        font-size: 16px;
        color: var(--text-primary-100000000, #000);
        font-weight: bold;
        line-height: 150%;
      }

      &__nudge {
        border-radius: 5px;
        border: 1px solid;
        font-size: 12px;
        color: var(--text-primary-100000000, #000);
        padding: 3px 9px;
        width: 100px;
        text-align: center;
        background-color: #faf5ff;
        border-color: #f3e8ff;
      }
    }

    &__action {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: 8px;
      position: relative;

      &__cost {
        font-size: 18px;
        color: var(--text-primary-100000000, #000);
        font-weight: bold;
        line-height: 150%;
      }

      &__button {
        height: auto;
        padding: 4px 8px;
        width: 100px;
      }

      .ribbon {
        top: -50%;
        right: -10%;
        transform: rotate(20deg);
        font-size: 0.65rem;
      }
    }
  }

  &__alternate__number {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    border-top: 2px solid;
    border-color: #e5e7eb;
    padding-top: 1.2rem;

    &__prompt {
      width: 100%;
      color: var(--text-primary-100000000, #000);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
    }
  }
}
</style>
