<script setup>
import { onMounted, onUnmounted, reactive, computed } from "vue";
import bus from "@/bus";
import { useAuthStore } from "@/stores/auth.js";

import ButtonMain from "@/components/buttons/ButtonMain.vue";
import ModuleTopUp from "@/components/ModuleTopUp.vue";
import PreloaderSpinner from "@/components/PreloaderSpinner.vue";

import { instachat, showLike } from "@/api/repositories/actionsPaid.js";
import { useRouter } from "vue-router";
import { useBalanceStore } from "../stores/balance";
import { useProfileStore } from "@/stores/profile";
import { useProfileBoostsStore } from "@/stores/profile_boosts";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";
import CreditsIcon from "@/assets/icons/credits.svg";

const authStore = useAuthStore();
const balanceStore = useBalanceStore();
const profileStore = useProfileStore();
const profileBoostStore = useProfileBoostsStore();

const data = reactive({
  actionType: null,
  actionData: undefined,
  initLoading: true,
});

// Function to determine required credits for different actions
const getRequiredCreditsForAction = (actionType) => {
  if (!actionType) return 0;

  switch (actionType) {
    case "instachat.init.dialog":
      return 3;
    case "like.show.profile":
      return 1;
    case "boost.buy.package":
      return data.actionData?.price || 5;
    default:
      return 0;
  }
};

onMounted(() => {
  bus.global.on("confirmation.paid.actions:request", (properties) => {
    data.initLoading = true;
    data.actionType = properties.actionType;
    data.actionData = properties.actionData;

    profileStore
      .fetchProfile()
      .catch(() => {
        bus.global.emit("notification.top:show", {
          type: "error",
          message:
            "Something went wrong while initializing confirmation, try again",
        });
        bus.global.emit("confirmation.paid.actions:rejected");
      })
      .finally(() => {
        data.initLoading = false;
      });
    // rework on backend to only fetch balance from the appropriete endpoint
  });
});

onUnmounted(() => {
  bus.global.off("confirmation.paid.actions:request");
});

const router = useRouter();

const setInitLoading = (state, delay = 0) => {
  setTimeout(() => {
    data.initLoading = state;
  }, delay);
};

const actionTypeComputedConstants = {
  "instachat.init.dialog": ({ balance }) => {
    const isBoostedProfile = data.actionData?.is_boosted_profile;
    const cost = 3;
    const logEventData = {
      userId: data.actionData.userId,
      purchase_cost: cost,
      credit_balance: balance,
      is_boosted_profile: isBoostedProfile,
      verified: data.actionData.verified,
      number_of_images: data.actionData.number_of_images,
      description_length: data.actionData.description_length,
      profile_actionable: data.actionData.profile_actionable,
    };

    logEvent(trackedEvents.INSTA_CHAT_OPENED, logEventData);

    return {
      title: "Instachat",
      description: isBoostedProfile
        ? `Start a chat instantly for free. Jump into the conversation now!`
        : balance < 3
        ? `Skipping the swiping and sending a chat instantly costs <span class='highlight'>${cost} credits</span>. Top up now and start connecting right away, no match needed.`
        : `Skip the swiping and start a chat instantly for <span class='highlight'>${cost} credits</span>.`,
      showTopUp: isBoostedProfile ? false : balance < 3,
      confirm: async () => {
        setInitLoading(true);
        return instachat({
          userId: data.actionData.userId,
        })
          .then((response) => {
            logEvent(trackedEvents.INSTA_CHAT_PURCHASED, {
              ...logEventData,
              credit_balance: balanceStore.getBalance,
            });
            bus.global.emit("confirmation.paid.actions:confirmed", response);
            router
              .push({
                name: "dialog",
                params: { dialogId: response.dialog.id },
              })
              .finally(() => {
                setInitLoading(false, 500);
              });
          })
          .catch(() => {
            bus.global.emit("notification.top:show", {
              type: "error",
              message:
                "Something went wrong while confirming this action, try again",
            });
            bus.global.emit("confirmation.paid.actions:rejected");
            setInitLoading(false, 500);
          });
      },
    };
  },
  "like.show.profile": ({ balance }) => {
    const cost = 1;

    const logEventData = {
      purchase_cost: cost,
      credit_balance: balance,
    };
    logEvent(trackedEvents.LIKE_REVEAL_OPENED, logEventData);

    return {
      title: "Who liked you?",
      description:
        balance < 1
          ? `Seeing who liked you costs <span class='highlight'>${cost} credit</span>. Top up now to skip the wait and see who liked you!`
          : `Skip the swiping and see who liked you for <span class='highlight'>${cost} credit</span>!`,
      showTopUp: balance < 1,
      confirm: () => {
        setInitLoading(true);
        showLike(data.actionData)
          .then((response) => {
            logEvent(trackedEvents.LIKE_REVEAL_PURCHASED, {
              ...logEventData,
              credit_balance: balanceStore.getBalance,
            });
            bus.global.emit("confirmation.paid.actions:confirmed", response);
            setInitLoading(false, 500);
          })
          .catch(() => {
            bus.global.emit("notification.top:show", {
              type: "error",
              message:
                "Something went wrong while confirming this action, try again",
            });
            bus.global.emit("confirmation.paid.actions:rejected");
            setInitLoading(false, 500);
          });
      },
    };
  },
  "boost.buy.package": ({ balance }) => {
    const cost = data.actionData.price;
    const logEventData = {
      boost_name: data.actionData.name,
      boost_price: data.actionData.price,
    };
    logEvent(trackedEvents.BUY_BOOST_CLICKED, logEventData);
    return {
      title: "Profile Boost",
      description:
        balance < cost
          ? `Buying this boost package costs <span class='highlight'>${cost} credits</span>. Top up now and start receiving more messages.`
          : `Buy this boost package for <span class='highlight'>${cost} credits</span> and start receiving more messages.`,
      showTopUp: balance < cost,
      confirm: async () => {
        setInitLoading(true);
        return profileBoostStore
          .purchaseBoostPackage(data.actionData.id)
          .then((response) => {
            logEvent(trackedEvents.BOOST_BOUGHT, {
              ...logEventData,
              credit_balance: balanceStore.getBalance,
            });
            bus.global.emit("confirmation.paid.actions:confirmed", response);
          })
          .catch(() => {
            bus.global.emit("notification.top:show", {
              type: "error",
              message:
                "Something went wrong while confirming this action, try again",
            });
            bus.global.emit("confirmation.paid.actions:rejected");
            setInitLoading(false, 500);
          });
      },
    };
  },
};

const paidActionData = computed(() => {
  if (!data || !data.actionType) {
    return {
      title: null,
      description: null,
      showTopUp: null,
      confirm: null,
    };
  }

  const gender = authStore.getUser().gender;
  const balance = balanceStore.getBalance;
  const paidData = actionTypeComputedConstants[data.actionType]({
    balance,
    gender,
  });
  return paidData;
});
</script>

<template>
  <div class="confirmation">
    <div v-if="data.initLoading" class="confirmation__body">
      <PreloaderSpinner dark />
    </div>

    <div
      v-if="!data.initLoading && !paidActionData.showTopUp"
      class="confirmation__body"
    >
      <div class="confirmation__body__title">
        {{ paidActionData.title }}
        <div class="confirmation__body__balance">
          <span>Current balance</span>
          <div class="credits-balance">
            <CreditsIcon class="credits-icon-small" />
            <span>
              {{ balanceStore.getBalance }}
              {{ balanceStore.getBalance === 1 ? "credit" : "credits" }}
            </span>
          </div>
        </div>
      </div>

      <!-- eslint-disable vue/no-v-html -->
      <div
        class="confirmation__body__description"
        v-html="paidActionData.description"
      ></div>
      <!-- eslint-enable vue/no-v-html -->
      <button-main
        class="confirmation__body__confirm"
        styling="gradient"
        :loading="data.initLoading"
        @click="paidActionData.confirm"
      >
        Confirm
      </button-main>
    </div>

    <div
      v-if="!data.initLoading && paidActionData.showTopUp"
      class="confirmation__body"
    >
      <!-- eslint-disable vue/no-v-html -->
      <h1 class="confirmation__body__title">
        {{ paidActionData.title }}
        <div class="confirmation__body__balance">
          <span>Current balance</span>
          <div class="credits-balance">
            <CreditsIcon class="credits-icon-small" />
            <span>
              {{ balanceStore.getBalance }}
              {{ balanceStore.getBalance === 1 ? "credit" : "credits" }}
            </span>
          </div>
        </div>
      </h1>
      <div
        class="confirmation__body__description"
        v-html="paidActionData.description"
      ></div>
      <!-- eslint-enable vue/no-v-html -->
      <ModuleTopUp
        class="confirmation__body__topup"
        :opened-from="data.actionType"
        :required-credits="getRequiredCreditsForAction(data.actionType)"
      />
    </div>
  </div>
</template>

<style lang="scss">
.confirmation__body__description .highlight {
  font-weight: bold;
}
</style>

<style lang="scss" scoped>
.credits-icon {
  width: 24px;
  fill: #e94057;
}

.credits-icon-small {
  width: 16px;
  fill: #e94057;
  margin-right: 2px;
}

.confirmation {
  width: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 1.5rem;
}

.confirmation__body {
  width: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &__confirm {
    margin-top: 0.5rem;
  }

  &__topup {
    width: 100%;
    margin-top: 1.5rem;
  }
}

.confirmation__body__balance {
  display: flex;
  flex-direction: column;
  row-gap: 2px;

  & > span {
    font-size: 12px;
    font-weight: 700;
  }

  & > div {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: auto;
    column-gap: 5px;
    color: #000;

    & > svg {
      color: rgba(233, 64, 87, 1);
    }

    & > span {
      font-size: 14px;
      font-weight: 700;
    }
  }
}

.confirmation__body__main-title {
  color: #000;
  text-align: center;
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: 150%; /* 42px */
}

.confirmation__body__title {
  width: 100%;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.confirmation__body__description {
  width: 100%;
  color: var(--text-primary-100000000, #000);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%; /* 21px */
}
</style>
