<template>
  <div class="prompts-editor">
    <div class="header">
      <h3 class="header__title">{{ prompt.property_name }}</h3>
    </div>

    <div v-if="prompt.validation_type === 'text'" class="prompts-editor__text">
      <InputField :errors="formService.errors.get('prompt')">
        <InputTextarea
          v-model="textValue"
          required
          type="text"
          name="prompt"
          placeholder="Your answer..."
          :error="formService.errors.has('prompt')"
          @enter="saveTextPrompt"
        />
      </InputField>
    </div>

    <div v-if="prompt.validation_type === 'link'" class="prompts-editor__image">
      <div class="prompt-photo-wrapper">
        <div class="prompt-photo">
          <div v-if="imageValue" class="has_photo">
            <div class="has_photo__wrapper">
              <SababuuImage :source="imageValue" />
              <span class="has_photo_edit" @click="openUploader">
                <PencilIcon />
              </span>
            </div>
          </div>
          <div v-else class="no_photo" @click="openUploader">
            <PlusHeroIcon />
            <span>Add photo</span>
          </div>
        </div>
      </div>
      <SababuuImageUploaderNew
        :direct-open-event="'open-prompt-img-uploader'"
        @uploaded="imageUploaded"
        @upload-error="
          (error) => {
            bus.global.emit('notification.top:show', {
              type: 'error',
              message: error,
            });
          }
        "
      />
    </div>

    <div class="prompts-editor__actions">
      <ButtonMain
        :loading="isUpdating"
        :disabled="isUpdating"
        @click="
          prompt.validation_type === 'text'
            ? saveTextPrompt()
            : saveImagePrompt()
        "
      >
        Save
      </ButtonMain>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import bus from "@/bus";

import FormService from "@/services/Form/Form.js";
import InputField from "@/components/InputField.vue";
import InputTextarea from "@/components/InputTextarea.vue";
import SababuuImage from "@/components/SababuuImage.vue";

import { usePropertiesStore } from "@/stores/properties";

import { stringRequired } from "@/services/Form/rules.js";

import ButtonMain from "@/components/buttons/ButtonMain.vue";
import SababuuImageUploaderNew from "@/components/SababuuImageUploaderNew.vue";
import PlusHeroIcon from "@/assets/icons/plus-hero-icon.svg";
import PencilIcon from "@/assets/icons/pencil.svg";

import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";

const propertiesStore = usePropertiesStore();

const props = defineProps({
  prompt: {
    type: Object,
    required: true,
  },
  close: {
    type: Function,
    required: true,
  },
});

const formService = new FormService({
  prompt: [stringRequired],
});

const textValue = ref(
  props.prompt.validation_type === "text" && props.prompt.value
);
const imageValue = ref(
  props.prompt.validation_type === "link" && props.prompt.value
);
const isUpdating = ref(false);

const saveTextPrompt = async () => {
  if (isUpdating.value === true) return;
  isUpdating.value = true;

  try {
    await formService.validate({ prompt: textValue.value });
    await propertiesStore.syncUserProperties([
      {
        property_id: props.prompt.property_id,
        values: [textValue.value],
      },
    ]);

    logEvent(trackedEvents.OWN_PROFILE_UPDATED, {
      info: "text-prompt",
    });
    props.close();
  } catch (error) {
    console.error("Failed to save text prompt:", error);
  } finally {
    isUpdating.value = false;
  }
};

const imageUploaded = (result) => {
  imageValue.value = result.info?.secure_url
    ? result.info.secure_url
    : result.secure_url;
};

const saveImagePrompt = async () => {
  if (isUpdating.value === true) return;
  isUpdating.value = true;

  try {
    await propertiesStore.syncUserProperties([
      {
        property_id: props.prompt.property_id,
        values: [imageValue.value],
      },
    ]);

    logEvent(trackedEvents.OWN_PROFILE_UPDATED, {
      info: "image-prompt",
    });
    props.close();
  } catch (error) {
    console.error("Failed to save link prompt:", error);
  } finally {
    isUpdating.value = false;
  }
};

const openUploader = () => {
  bus.global.emit("open-prompt-img-uploader");
};
</script>

<style lang="scss" scoped>
.prompts-editor {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    &__title {
      font-size: 1.25rem;
      font-weight: 600;
    }
  }

  &__text {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
  }

  &__image {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
  }

  &__link__question__add {
    background: white;
    border: 2px dashed;
    border-color: rgb(0 0 0 / 0.2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    row-gap: 5px;
    border-radius: 5px;
    color: #666;
    height: 200px;

    & > svg {
      height: 20px;
      width: 20px;
    }

    & > span {
      font-size: 12px;
    }
  }
}

.prompt-photo-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.prompt-photo {
  width: 9.5rem;

  .has_photo {
    position: relative;
    height: 11.875rem;

    &__wrapper {
      height: 100%;
      width: 100%;
      background-color: #f0f0f0;
      border-radius: 5px;
      position: relative;

      & > img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 5px;
      }

      .has_photo_edit {
        height: 24px;
        width: 24px;
        border-radius: 50%;
        background-color: #fff;
        border: 1px solid #ebebeb;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #000;
        position: absolute;
        right: -5px;
        top: -8px;

        & > svg {
          height: 16px;
          width: 16px;
        }
      }
    }

    & > svg {
      position: absolute;
      height: 1.875rem;
      width: 1.875rem;
      bottom: -0.6rem;
      right: -0.6rem;
      color: $primary-color;
    }
  }

  .no_photo {
    position: relative;
    height: 11.875rem;
    border-radius: 5px;
    border: 2px dashed #dcdfe6;
    color: #666;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    & > svg {
      height: 20px;
      width: 20px;
    }

    & > span {
      font-size: 12px;
    }
  }
}

.prompts-editor__actions {
  display: flex;
  margin-top: 1.5rem;
}
</style>
