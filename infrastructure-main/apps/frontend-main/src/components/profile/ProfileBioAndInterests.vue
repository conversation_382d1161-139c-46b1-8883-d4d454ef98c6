<template>
  <div class="bio-and-interests">
    <div v-if="bioProperties.length > 0" class="section">
      <h2 class="section-title">More about me</h2>
      <BioList
        :bio-property-fields="propertiesStore.bioFields"
        :user-bio-properties="bioProperties"
      />
    </div>
    <div v-if="interestsProperties.length > 0" class="section">
      <h2 class="section-title">My interests</h2>
      <InterestsList :user-interests-properties="interestsProperties" />
    </div>
  </div>
</template>

<script setup>
import { usePropertiesStore } from "@/stores/properties";

import BioList from "@/components/profile/bio/BioList.vue";
import InterestsList from "@/components/profile/interests/InterestsList.vue";

const propertiesStore = usePropertiesStore();

defineProps({
  bioProperties: {
    type: Array,
    default: () => [],
  },
  interestsProperties: {
    type: Array,
    default: () => [],
  },
});
</script>

<style lang="scss" scoped>
.bio-and-interests {
  box-shadow: $profile-card-item-shadow-sm;
  background-color: white;
  border: 1px solid $profile-card-item-border-color;
  border-radius: $app-card-border-radius;
  padding: 16px;
  display: flex;
  flex-direction: column;
  row-gap: 1rem !important;

  .section-title {
    font-weight: bolder;
    font-size: 14px;
    margin-bottom: 0.5rem;
  }
}
</style>
