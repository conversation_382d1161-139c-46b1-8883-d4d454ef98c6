<template>
  <div class="profile-actionable">
    <slot />
    <div class="profile-actionable__actions">
      <slot v-if="props.canDoActions" name="gift"></slot>
      <div
        v-if="user.actionable && props.canDoActions"
        class="profile-actionable__actions__action"
      >
        <LikeIcon @click="openCommentsEditor" />
      </div>
    </div>
    <ModalBottom
      :closable="true"
      :name="`actionable.comment.${props.actionableId}`"
      :padding-s-m="true"
    >
      <div class="actionable-wrapper">
        <div class="actionable-item">
          <slot />
        </div>
        <div class="actionable-prompt">
          <p>
            Tell <b>{{ props.user.name }}</b> why you like
            {{ props.user.gender === "female" ? "her" : "his" }}
            {{ props.actionableName }}
          </p>
        </div>
        <div class="actionable-comment">
          <div class="comment">
            <InputText
              v-model="comment"
              type="text"
              name="comment"
              placeholder="Optional comment..."
              @enter="performAction"
            />
          </div>
          <ButtonSquare
            class="like"
            :loading="profileStore.isPerformingLike"
            :disabled="profileStore.isPerformingLike"
            @click="performAction"
          >
            <LikeIcon />
          </ButtonSquare>
        </div>
      </div>
    </ModalBottom>
  </div>
</template>

<script setup>
import { ref } from "vue";
import bus from "@/bus.js";

import { useProfilesStore } from "@/stores/profiles";
import { useProfileStore } from "@/stores/profile";

import ModalBottom from "@/components/ModalBottom.vue";

import ButtonSquare from "@/components/buttons/ButtonSquare.vue";
import InputText from "@/components/InputText.vue";

import LikeIcon from "@/assets/icons/like.svg";

import { isUserNetworkActive } from "@/utils/helpers";
import { subscribeUserToPush as subscribeUserToWebPush } from "@/utils/notifications.js";
import { scrollToTop } from "@/utils/helpers";

import { ACTIONABLE_TYPES } from "@/constants";
import { useRoute, useRouter } from "vue-router";

const profilesStore = useProfilesStore();
const profileStore = useProfileStore();
const emit = defineEmits(["action"]);

const props = defineProps({
  actionableId: {
    type: Number,
    required: true,
  },
  actionableType: {
    type: String,
    validator: (value) => {
      return Object.values(ACTIONABLE_TYPES).includes(value);
    },
    default: ACTIONABLE_TYPES.USER,
  },
  actionableName: {
    type: String,
    required: true,
  },
  canDoActions: {
    type: Boolean,
    default: true,
  },
  user: {
    type: Object,
    required: true,
  },
});

const comment = ref("");

const currentRoute = useRoute();
const router = useRouter();
const openCommentsEditor = () => {
  bus.global.emit("modal.bottom:open", {
    name: `actionable.comment.${props.actionableId}`,
  });
};

const performAction = () => {
  if (profileStore.isPerformingLike) return;
  if (!isUserNetworkActive()) {
    bus.global.emit("notification.top:show", {
      type: "error",
      message: "You are offline",
    });
    return;
  }

  profileStore.isPerformingLike = true;
  /*
   * If on native app skip this web push
   * as native push will be requested.
   */
  if (
    !profileStore.isPushNotificationSubscribed &&
    window.SababuuNativeBridge === undefined
  ) {
    subscribeUserToWebPush();
    profileStore.setPushSubscribe(true);
  }
  scrollToTop();
  bus.global.emit("modal.bottom:close", {
    name: `actionable.comment.${props.actionableId}`,
  });
  profilesStore
    .likeProfile(
      props.actionableId,
      props.actionableType,
      comment.value.trim(),
      props.user,
      props.user.is_boosted,
      currentRoute.name !== "profile.guest" // don't emit match notification on guest profile
    )
    .then((result) => {
      if (currentRoute.name === "profile.guest" && result.dialog_id) {
        router.push({ name: "dialog", params: { dialogId: result.dialog_id } });
      }
    })
    .finally(() => {
      profileStore.isPerformingLike = false;
    });
  profilesStore.removeProfile(props.user.id, "slideRight");
  emit("action");
};
</script>

<style lang="scss" scoped>
.profile-actionable {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;

  &__actions {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    row-gap: 0.875rem;

    z-index: 10;

    &__action {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      width: 40px;
      border-radius: 40px;
      box-shadow: $profile-card-item-shadow-lg;
      background: white;
      & > svg {
        width: 26px;
        height: 26px;
      }
    }
  }
}

.actionable-wrapper {
  display: flex;
  flex-direction: column;
  row-gap: 1rem;

  .actionable-item {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 5px;

    & ::v-deep(.property-card) {
      margin-bottom: 0 !important;
    }

    & ::v-deep(img) {
      border-radius: $app-card-border-radius;
    }
  }

  .actionable-prompt {
    font-size: 14px;
  }

  .actionable-comment {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    column-gap: 0.875rem;

    .comment {
      flex: 1;
    }

    .like {
      & > svg {
        height: 24px;
        width: 24px;
      }
    }
  }
}
</style>
