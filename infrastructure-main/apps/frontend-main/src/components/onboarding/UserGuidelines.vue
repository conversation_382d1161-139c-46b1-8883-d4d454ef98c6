<script setup>
import { onMounted } from "vue";

import { useAuthStore } from "@/stores/auth";
import { useProfilesStore } from "@/stores/profiles";

import { logEvent } from "@/utils/tracking";
import trackedEvents from "@/utils/trackedEvents";

import BrandIcon from "@/assets/brand/sababuu/icon.svg";
import CheckIcon from "@/assets/icons/check.svg";
import ButtonMain from "@/components/buttons/ButtonMain.vue";

const emit = defineEmits(["completed"]);

const authStore = useAuthStore();
const profilesStore = useProfilesStore();

const completeGuideline = () => {
  authStore.setUserRegisterIsCompleted();
  emit("completed");
};

onMounted(() => {
  logEvent(trackedEvents.ACCOUNT_CREATED, {
    gender: authStore.user.gender,
  });
  let location = null;
  if (
    authStore.completeRegisterData.latitude &&
    authStore.completeRegisterData.longitude
  ) {
    location = {
      latitude: authStore.completeRegisterData.latitude,
      longitude: authStore.completeRegisterData.longitude,
    };
  }
  profilesStore.fetchInitialProfiles(location || {}, true).catch({
    /* ignore */
  });
});
</script>

<template>
  <div class="onboarding">
    <div class="onboarding__details">
      <div class="onboarding__details__icon">
        <BrandIcon />
      </div>
      <h1>Welcome to Sababuu.</h1>
      <p>Please follow these house rules.</p>
      <div class="onboarding__details__rule">
        <div class="title">
          <CheckIcon />
          <h3>Be yourself</h3>
        </div>
        <p>
          Make sure your photos, age and description are true to who you are.
        </p>
      </div>
      <div class="onboarding__details__rule">
        <div class="title">
          <CheckIcon />
          <h3>Stay safe</h3>
        </div>
        <p>
          Don't be too quick to give out personal information. Date and chat
          safely.
        </p>
      </div>
      <div class="onboarding__details__rule">
        <div class="title">
          <CheckIcon />
          <h3>Be thoughtful</h3>
        </div>
        <p>
          Respect others and treat them as you would like to be treated.
          Thoughtful liking and chatting leads to better dates.
        </p>
      </div>
    </div>
    <div class="onboarding__actions">
      <button-main class="onboarding__actions__next" @click="completeGuideline">
        Start Liking
      </button-main>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.onboarding {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;

  &__details {
    padding-top: 2rem;

    &__icon {
      margin-bottom: 0.5rem;

      & svg {
        color: $primary-color;
        height: 2.5rem;
        width: 2.5rem;
      }
    }
    & > h1 {
      font-size: 1.5rem;
      font-weight: bold;
    }

    & > p {
      font-size: 18px;
      margin-top: 0.3rem;
      margin-bottom: 3rem;
    }

    &__rule {
      margin-bottom: 1.5rem;

      & .title {
        display: flex;
        align-items: center;
        column-gap: 0.5rem;

        & h3 {
          font-size: 16px;
          font-weight: bold;
        }

        & svg {
          color: $primary-color;
          height: 1.2rem;
          width: 1.2rem;
        }
      }

      & p {
        margin-top: 0.3rem;
        font-size: 16px;
      }
    }
  }

  &__actions {
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
    align-items: center;
    padding-bottom: 16px;

    &__next {
      display: flex;
      width: 100%;

      &:disabled {
        opacity: 0.6;
      }
    }
  }
}
</style>
