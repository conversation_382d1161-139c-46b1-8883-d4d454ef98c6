import { createApp } from "vue";
import router from "@/router";
import { createPinia } from "pinia";
import { registerSW } from "virtual:pwa-register";

import "./style.scss";
import App from "./App.vue";

import socket from "@/plugins/socket";
import filters from "@/plugins/filters";
import i18n from "@/plugins/i18n";
import confirmation from "@/plugins/confirmation";
import feedConfirmation from "@/plugins/feedConfirmation";
import posthog from "@/plugins/posthog.js";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import "element-plus/dist/index.css";
import {
  ElSwitch,
  ElSlider,
  ElDatePicker,
  ElTooltip,
} from "element-plus";
import VueTelInput from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";
import "@/utils/reactNativeBus";
import config from "@/config";

createApp(App)
  .use(router)
  .use(createPinia().use(piniaPluginPersistedstate))
  .use(i18n)
  .use(socket)
  .use(filters)
  .use(confirmation)
  .use(feedConfirmation)
  .use(posthog, {
    apiKey: config.posthog.key,
    router,
  })
  .component(ElSwitch.name, ElSwitch)
  .component(ElSlider.name, ElSlider)
  .component(ElDatePicker.name, ElDatePicker)
  .component(ElTooltip.name, ElTooltip)
  .use(VueTelInput, {
    mode: "international",
  })
  .mount("#app");

const intervalMS = 60 * 60 * 1000;

registerSW({
  immediate: true,
  onRegisteredSW(swUrl, r) {
    r &&
      setInterval(async () => {
        if (!(!r.installing && navigator)) return;

        if ("connection" in navigator && !navigator.onLine) return;

        const resp = await fetch(swUrl, {
          cache: "no-store",
          headers: {
            cache: "no-store",
            "cache-control": "no-cache",
          },
        });

        if (resp?.status === 200) await r.update();
      }, intervalMS);
  },
});
