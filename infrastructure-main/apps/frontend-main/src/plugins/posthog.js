import { nextTick } from "vue";
import posthog from "posthog-js";
import trackedEvents, {
  isValidEvent,
  metaPageIds,
} from "@/utils/trackedEvents";
import { getDisplayMode } from "@/utils/helpers.js";

/**
 * @type posthog
 */
let posthogInstance;

export const initialize = (apiKey) => {
  posthogInstance = posthog.init(apiKey, {
    api_host: "https://us.i.posthog.com",
    person_profiles: "identified_only",
    capture_pageview: false,
    disable_session_recording: true,
  });
  // posthogInstance.featureFlags.overrideFeatureFlags({
  //   flags: {
  //     "layout-button-and-online-format": "compact",
  //   },
  // });
};

export const trackEvent = (event, properties) => {
  if (!isValidEvent(event)) {
    // console.warn("Posthog skipping event -> ", event);
    return;
  }

  if (posthogInstance) {
    // console.log("Posthog -> ", event, properties);
    posthogInstance.capture(event, properties);

    if (event === trackedEvents.LOGGED_OUT) {
      posthogInstance.reset();
    }
  } else {
    console.warn("Posthog analytics is not initialized");
  }

  if (window.SababuuNativeBridge?.firebaseLogEvent) {
    const { mappedFirebaseEvent, firebaseEventValue = {} } = mapToFirebaseROAS(
      event,
      properties
    );
    window.SababuuNativeBridge.firebaseLogEvent(mappedFirebaseEvent, {
      ...properties,
      ...firebaseEventValue,
    });
  }

  if (window.SababuuNativeBridge?.metaLogEvent) {
    const { mappedMetaEvent = null, metaEventProperties = {} } =
      mapToMetaEvent(event);
    if (mappedMetaEvent) {
      window.SababuuNativeBridge.metaLogEvent(
        mappedMetaEvent,
        metaEventProperties
      );
    }
  }
};

function mapToFirebaseROAS(event, properties) {
  if (event === trackedEvents.ACCOUNT_CREATED) {
    const { gender } = properties;
    return {
      mappedFirebaseEvent: `${trackedEvents.ACCOUNT_CREATED}_${gender}`,
      firebaseEventValue: {
        value: gender === "female" ? 0.075 : 0.025,
        currency: "EUR",
      },
    };
  }

  return {
    mappedFirebaseEvent: event,
  };
}

function mapToMetaEvent(event) {
  if (event === trackedEvents.ACCOUNT_CREATED) {
    return {
      mappedMetaEvent: "CompleteRegistration",
      metaEventProperties: {
        registrationMethod: "phoneNumber",
      },
    };
  }

  const metaPageId = metaPageIds[event];
  if (metaPageId) {
    return {
      mappedMetaEvent: "ViewedContent",
      metaEventProperties: {
        contentType: "page",
        contentId: metaPageId,
      },
    };
  }

  return {};
}

export const startSessionRecording = () => {
  if (posthogInstance) {
    posthogInstance.startSessionRecording();
  } else {
    console.warn("Posthog analytics is not initialized");
  }
};

export const getPosthogPerson = (user) => {
  return {
    phone: user.phone,
    country: user.country,
    createdAt: user.created_at,
    gender: user.gender,
    birthday: user.birthday,
    profileVerified: user.person_verification,
  };
};

export const identifyUser = (
  userId,
  traits,
  { startRecording, registerCompleted } = {
    startRecording: false,
    registerCompleted: false,
  }
) => {
  if (posthogInstance) {
    // console.log("Posthog identifying -> ", userId, traits);
    posthogInstance.identify(userId, {
      display_mode: getDisplayMode(),
      onboarding_completed: registerCompleted,
      ...traits,
    });
    if (startRecording) {
      startSessionRecording();
    }
  } else {
    console.warn("Posthog analytics is not initialized");
  }
};

export const resetPosthogUser = () => {
  if (posthogInstance) {
    posthogInstance.reset();
  }
};

export const isFeatureEnabled = (feature) => {
  return new Promise((resolve) => {
    if (posthogInstance) {
      posthogInstance.onFeatureFlags(() => {
        if (posthog.isFeatureEnabled(feature)) {
          resolve(true);
        } else {
          resolve(false);
        }
      });
    } else {
      console.warn("Posthog analytics is not initialized");
      resolve(false);
    }
  });
};

export const getFeatureFlag = (feature) => {
  return new Promise((resolve) => {
    if (posthogInstance) {
      posthogInstance.onFeatureFlags(() => {
        const flag = posthog.getFeatureFlag(feature);
        resolve(flag);
      });
    } else {
      console.warn("Posthog analytics is not initialized");
      resolve(undefined);
    }
  });
};

export const captureException = (error) => {
  console.error(error);
  if (posthogInstance) {
    posthogInstance.captureException(error);
  } else {
    console.warn("Posthog analytics is not initialized");
  }
};

export const getActiveMatchingSurveys = () => {
  return new Promise((resolve, reject) => {
    if (posthogInstance) {
      posthogInstance.getActiveMatchingSurveys((surveys) => {
        resolve(surveys);
      }, true);
    } else {
      console.warn("Posthog analytics is not initialized");
      reject(undefined);
    }
  });
};

export const answerSurvey = (properties) => {
  if (posthogInstance) {
    posthogInstance.capture("survey sent", properties);
  } else {
    console.warn("Posthog analytics is not initialized");
  }
};

export const dismissedSurvey = (properties) => {
  if (posthogInstance) {
    posthogInstance.capture("survey dismissed", properties);
  } else {
    console.warn("Posthog analytics is not initialized");
  }
};

export const shownSurvey = (properties) => {
  if (posthogInstance) {
    posthogInstance.capture("survey shown", properties);
  } else {
    console.warn("Posthog analytics is not initialized");
  }
};

export default {
  install(app, options) {
    if (!options.apiKey) {
      console.error("Posthog apiKey is required");
      return;
    }

    initialize(options.apiKey);

    app.config.globalProperties.$posthog = posthogInstance;

    if (options.router) {
      options.router.beforeEach((to, from) => {
        app.config.globalProperties.$posthog.capture("$pageleave", {
          display_mode: getDisplayMode(),
          path: from.fullPath,
        });
        return true;
      });

      options.router.afterEach((to, from, failure) => {
        if (!failure) {
          nextTick(() => {
            app.config.globalProperties.$posthog.capture("$pageview", {
              display_mode: getDisplayMode(),
              path: to.fullPath,
            });
          });
        }
      });
    }
  },
};
