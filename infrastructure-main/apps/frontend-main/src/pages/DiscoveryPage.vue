<script setup>
import { computed, watch, onBeforeMount, ref, onUnmounted } from "vue";
import moment from "moment";
import bus from "@/bus";
import { useProfilesStore } from "@/stores/profiles";
import { useProfileStore } from "@/stores/profile";
import { useInitialize } from "@/composables/useInitialize";
import { useNativeCheckLocationPermission } from "@/composables/useNativeCheckLocationPermission";

import {
  getCurrentLocation,
  IS_CLOUDINARY_URL,
  scrollToTop,
} from "@/utils/helpers";
import { logEvent } from "@/utils/tracking";
import trackedEvents from "@/utils/trackedEvents";
import { registerNativeHandler } from "@/utils/reactNativeBus";
import ProfileCard from "@/components/ProfileCard.vue";
import SurveyCard from "@/components/SurveyCard.vue";
import UpdateCard from "@/components/update-card/UpdateCard.vue";

import MainLayout from "@/layouts/MainLayout.vue";
import ModalOverlay from "@/components/ModalOverlay.vue";
import ModalChatActions from "@/components/ModalChatActions.vue";
import MatchNotification from "@/components/MatchNotification.vue";
import BoostedInstachat from "@/components/BoostedInstachat.vue";
import DiscoverySettings from "@/components/DiscoverySettings.vue";
import RequestLocationAccess from "@/components/RequestLocationAccess.vue";
import PreloaderHeart from "@/components/PreloaderHeart.vue";
import SettingsButton from "@/components/SettingsButton.vue";
import ReportModal from "@/components/ReportModal.vue";
import { getFeatureFlag } from "@/plugins/posthog";

useInitialize();
const profilesStore = useProfilesStore();
const profileStore = useProfileStore();

const activeProfile = computed(
  () =>
    (profilesStore.discoveryFeed.length > 0 &&
      profilesStore.discoveryFeed[0].type === "profile" &&
      profilesStore.discoveryFeed[0].item) ||
    null
);
const numberOfProfiles = computed(() => profilesStore.profiles.length);

watch(numberOfProfiles, (newValue) => {
  if (newValue === 0) {
    logEvent(trackedEvents.PROFILES_EMPTY);
  }
});

const { checkNativeLocationPermission } = useNativeCheckLocationPermission();

let lastSeenAtUpdateInterval;

const renderOrderVersion = ref(1);

onBeforeMount(async () => {
  const renderOrderFeatureFlag = await getFeatureFlag("render-order");
  if (renderOrderFeatureFlag === "test") renderOrderVersion.value = 2;

  if (window.SababuuNativeBridge) {
    profileStore.checkNativePush();

    let location = null;
    const { locationAccessGranted } = await checkNativeLocationPermission();
    if (locationAccessGranted === true) {
      location = await getNativeLocation();
    }

    await profilesStore.fetchInitialProfiles(location || {}).finally(() => {
      const numberOfProfiles = profilesStore.profiles.length;
      const hasCommunityMode =
        profileStore.isInCommunity &&
        profileStore.profile.settings.only_community;
      logEvent(trackedEvents.PROFILES_OPENED, {
        number_of_profiles: numberOfProfiles,
        has_shared_location: location !== null,
        ...(location !== null && {
          location_longitude: location.longitude,
          location_latitude: location.latitude,
        }),
        has_community_mode: hasCommunityMode,
        ...(hasCommunityMode && {
          community_id: profileStore.community.id,
        }),
      });
    });
  } else {
    await getCurrentLocation((locationInfo, permissionGranted) => {
      return profilesStore.fetchInitialProfiles(locationInfo).finally(() => {
        const numberOfProfiles = profilesStore.profiles.length;
        const hasCommunityMode =
          profileStore.isInCommunity &&
          profileStore.profile.settings.only_community;
        logEvent(trackedEvents.PROFILES_OPENED, {
          number_of_profiles: numberOfProfiles,
          has_shared_location: permissionGranted,
          ...(permissionGranted && {
            location_longitude: locationInfo.longitude,
            location_latitude: locationInfo.latitude,
          }),
          has_community_mode: hasCommunityMode,
          ...(hasCommunityMode && {
            community_id: profileStore.community.id,
          }),
        });
      });
    });
  }

  // once 5 seconds rewrite last_seen_at to the same value to fire recomputation of last_seen_at
  lastSeenAtUpdateInterval = setInterval(() => {
    if (activeProfile.value?.last_seen_at) {
      profilesStore.updateProfile(activeProfile?.value?.id, {
        last_seen_at: moment.utc(activeProfile?.value?.last_seen_at, false),
      });
    }
  }, 5000);

  profilesStore.startMatchInterval();
});

onUnmounted(() => {
  if (lastSeenAtUpdateInterval) {
    clearInterval(lastSeenAtUpdateInterval);
  }
});

const deregisterLocation = ref(null);
const getNativeLocation = () =>
  new Promise((resolve) => {
    deregisterLocation.value = registerNativeHandler(
      "onUserLocation",
      (payload) => {
        deregisterLocation.value();

        const { status, data } = payload;
        if (status === "success") {
          const location =
            data && data.coords
              ? {
                  latitude: data.coords.latitude,
                  longitude: data.coords.longitude,
                }
              : null;
          resolve(location);
        } else {
          resolve(null);
        }
      }
    );
    window.SababuuNativeBridge.getUserLocation();
  });

const filtersUpdated = () => {
  profilesStore.resetProfiles();
  profilesStore.fetchInitialProfiles();
};

const openFilters = () => {
  bus.global.emit("modal.overlay:open", { name: "discovery-settings" });
};

const closeMatchNotification = () => {
  bus.global.emit("modal.overlay:close", {
    name: "match-notification",
  });
};

const closeBoostedInstachat = () => {
  bus.global.emit("modal.overlay:close", {
    name: "boosted-instachat",
  });
};

const cardAnimationStyles = (index) => ({
  discovery__cards__card_active: index === 0,
  discovery__cards__card_active_slide_right:
    index === 0 && profilesStore.actionAnimation === "slideRight",
  discovery__cards__card_active_slide_left:
    index === 0 && profilesStore.actionAnimation === "slideLeft",
  discovery__cards__card_next: index > 0,
  discovery__cards__card_next_triggered:
    index === 1 && profilesStore.actionAnimation,
});
const canLoadNativeRequestLocation = window.SababuuNativeBridge !== undefined;

const reportProfile = () => {
  scrollToTop();
  profilesStore.removeProfile(activeProfile.value.id, "slideLeft");
};
</script>

<template>
  <RequestLocationAccess v-if="canLoadNativeRequestLocation" />
  <ModalOverlay :closable="false" name="discovery-settings" :padding-s-m="true">
    <DiscoverySettings @updated="filtersUpdated" />
  </ModalOverlay>
  <ModalOverlay name="match-notification">
    <MatchNotification @close="closeMatchNotification" />
  </ModalOverlay>
  <ModalOverlay name="boosted-instachat">
    <BoostedInstachat @close="closeBoostedInstachat" />
  </ModalOverlay>
  <ModalChatActions name="reportAreYouSure">
    <ReportModal
      v-if="activeProfile"
      :profile="activeProfile"
      @reported="reportProfile"
    />
  </ModalChatActions>
  <main-layout class="discovery-page">
    <div class="discovery">
      <!-- Discovery Feed Card -->
      <template
        v-if="
          !profilesStore.profilesLoading &&
          profilesStore.discoveryFeed.length > 0
        "
      >
        <template
          v-for="(feedCard, index) in profilesStore.discoveryFeed"
          :key="feedCard.item.id"
        >
          <ProfileCard
            v-if="feedCard.type === 'profile'"
            :class="{
              is_cld_image: IS_CLOUDINARY_URL.test(feedCard.item.avatar),
              ...cardAnimationStyles(index),
            }"
            :profile="feedCard.item"
            :render-order-version="renderOrderVersion"
          >
            <!-- This is a hack given our current profile card structure
            Ideally this header should be fixed on the layout and allow
            for profile card content to scroll below it -->
            <template #header>
              <div class="discovery__header">
                <div
                  v-if="
                    profileStore.isInCommunity &&
                    profileStore.profile.settings.only_community
                  "
                  class="discovery__header__community"
                >
                  <div>
                    <span class="discovery__header__community__badge"
                      >Community Active</span
                    >
                  </div>
                  <span class="discovery__header__community__name">{{
                    profileStore.community.name
                  }}</span>
                </div>
                <SettingsButton />
              </div>
            </template>
          </ProfileCard>
          <SurveyCard
            v-if="feedCard.type === 'survey'"
            :class="{ ...cardAnimationStyles(index) }"
            :survey="feedCard.item"
          />
          <UpdateCard
            v-if="feedCard.type === 'update'"
            :class="{ ...cardAnimationStyles(index) }"
            :property="feedCard.item"
          />
        </template>
      </template>

      <!-- No Profiles -->
      <div
        v-if="
          !profilesStore.profilesLoading &&
          profilesStore.discoveryFeed.length === 0
        "
        class="discovery__empty"
      >
        <div class="discovery__header">
          <div
            v-if="
              profileStore.isInCommunity &&
              profileStore.profile.settings.only_community
            "
            class="discovery__header__community"
          >
            <div>
              <span class="discovery__header__community__badge"
                >Community Active</span
              >
            </div>
            <span class="discovery__header__community__name">{{
              profileStore.community.name
            }}</span>
          </div>
          <SettingsButton />
        </div>
        <div class="discovery__empty__content">
          <img src="@/assets/icons/checkback-later.gif" alt="" />
          <span class="placeholder__prompt">
            Check back later or
            <span class="change-filter" @click="openFilters"
              >change filter settings</span
            >
            to see more profiles.
          </span>
        </div>
      </div>

      <!-- Profiles Loading -->
      <div v-if="profilesStore.profilesLoading" class="discovery__loading">
        <PreloaderHeart text="Loading profiles ..." />
      </div>
    </div>
  </main-layout>
</template>

<style lang="scss" scoped>
.discovery-page {
  background-color: $profile-card-page-background;
}

.discovery {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
}

.discovery__header {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  column-gap: 1rem;
  padding: 8px 16px;
  position: absolute;
  top: 0;
  z-index: 30;

  &__community {
    padding: 5px 20px;
    background: #fff;
    display: flex;
    flex-direction: column;
    border-radius: 15px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);

    row-gap: 3px;

    & > div:first-child {
      display: flex;
      justify-content: center;
    }

    &__badge {
      display: inline-block;
      font-size: 12px;
      color: #e94057;
    }

    &__name {
      font-size: 12px;
      font-weight: bold;
      text-align: center;
    }
  }
}

.discovery__empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 8rem;

  &__content {
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    & > img {
      width: 70%;
    }

    .placeholder__prompt {
      text-align: center;
    }

    .change-filter {
      color: #e94057;
    }
  }
}

.discovery__loading {
  display: flex;
  height: 100%;
  padding-top: 8rem;
  flex-direction: column;
}

.discovery__cards__card_active {
  z-index: 2;
  transition: all 0.5s;
}

.discovery__cards__card_active_slide_right {
  margin-left: 300px;
  top: 50px;
  transform: translateX(20px);
  opacity: 0;
}

.discovery__cards__card_active_slide_left {
  margin-left: -300px;
  top: 50px;
  transform: translateX(-20px);
  opacity: 0;
}

.discovery__cards__card_next {
  opacity: 0;
  transform: scale(0.9) !important;
  z-index: 1;
  transition: all 0.5s !important;
  overflow: hidden;
  pointer-events: none;
}

.discovery__cards__card_next_triggered {
  opacity: 1;
  transform: scale(1);
  top: 20px;
  z-index: 2;
}
</style>
