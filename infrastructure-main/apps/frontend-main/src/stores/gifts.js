import { defineStore } from "pinia";
import { ref } from "vue";

import {
  getGifts as fetchGiftsReq,
  buyGift as buyGiftReq,
} from "@/api/repositories/gifts.js";

export const useGiftsStore = defineStore(
  "gifts",
  () => {
    const gifts = ref([]);
    const activeProfile = ref(null);
    const activeSource = ref(null);

    const setActiveProfile = (profile) => {
      activeProfile.value = profile;
    };

    const setActiveSource = (source) => {
      activeSource.value = source;
    };

    const fetchGifts = async () => {
      if (gifts.value.length > 0) return;
      const _gifts = await fetchGiftsReq();
      gifts.value = _gifts.sort((a, b) => a.cost - b.cost);
    };

    const buyGift = async (params) => {
      const { giftId, profileId } = params;
      await buyGiftReq(giftId, profileId);
    };

    return {
      gifts,
      activeProfile,
      activeSource,
      setActiveProfile,
      setActiveSource,
      fetchGifts,
      buyGift,
    };
  },
  {
    persist: {
      paths: ["gifts"],
    },
  }
);
