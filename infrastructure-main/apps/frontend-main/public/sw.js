import { clientsClaim } from "workbox-core";
import { cleanupOutdatedCaches, precacheAndRoute } from "workbox-precaching";
import { registerRoute } from "workbox-routing";
import { CacheFirst } from "workbox-strategies";
import { ExpirationPlugin } from "workbox-expiration";

cleanupOutdatedCaches();

precacheAndRoute(self.__WB_MANIFEST);

registerRoute(
  ({ url }) => {
    return (
      url.pathname.includes("preloaderSimple.png") ||
      url.pathname.includes("gift.png") ||
      url.pathname.includes("checkback-later.gif") ||
      url.href.includes("cdn-icons-png.freepik.com/256/4642/4642381.png") ||
      url.href.includes("cdn-icons-png.freepik.com/512/2926/2926754.png")
    );
  },
  new CacheFirst({
    cacheName: "critical-png-assets",
    plugins: [
      new ExpirationPlugin({
        maxEntries: 10,
        maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
      }),
    ],
  })
);

self.addEventListener("push", function (event) {
  if (!(self.Notification && self.Notification.permission === "granted")) {
    return;
  }

  const payload = event.data ? event.data.json() : {};
  event.waitUntil(self.registration.showNotification(payload.title, payload));
});

self.addEventListener("notificationclick", (event) => {
  const openUrl = (path) => {
    const urlToOpen = new URL(path, self.location.origin).href;

    const promiseChain = self.clients
      .matchAll({
        type: "window",
        includeUncontrolled: true,
      })
      .then((windowClients) => {
        let matchingClient = null;

        for (let i = 0; i < windowClients.length; i++) {
          const windowClient = windowClients[i];
          if (windowClient.url === urlToOpen) {
            matchingClient = windowClient;
            break;
          }
        }

        if (matchingClient) {
          return matchingClient.focus();
        } else {
          return self.clients.openWindow(urlToOpen);
        }
      });

    event.waitUntil(promiseChain);
  };

  switch (event.action || event.notification?.data?.action) {
    case "view_likes":
      openUrl("likes");
      break;
    case "view_matches":
      openUrl("dialogs");
      break;
    case "view_profile":
    case "view_gifts":
      openUrl("profile");
      break;
    case "open_dialog":
      openUrl(`dialogs/${event.notification.data?.dialogId}`);
      break;
    default:
      console.log(`Unknown action clicked: '${event.action}'`);
      break;
  }
});

self.skipWaiting();
clientsClaim();
