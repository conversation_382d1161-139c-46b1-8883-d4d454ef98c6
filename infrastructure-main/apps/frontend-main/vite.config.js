import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import VueI18nPlugin from "@intlify/unplugin-vue-i18n/vite";
import svgLoader from "vite-svg-loader";
import { VitePWA } from "vite-plugin-pwa";
import path from "path";

export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/styles/global.scss";',
      },
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          utilities: ["@vueuse/core"],
          element: ["element-plus"],
          telInput: ["vue-tel-input"],
          moment: ["moment"],
        },
        sourcemap: true,
      },
    },
  },
  plugins: [
    vue(),
    VueI18nPlugin({
      include: "./src/locales/**",
    }),
    VitePWA({
      strategies: "injectManifest",
      manifest: {
        id: "/",
        description: "The #1 free dating app",
        theme_color: "#ffffff",
        orientation: "portrait",
        icons: [
          {
            src: "pwa-64x64.png",
            sizes: "64x64",
            type: "image/png",
          },
          {
            src: "pwa-192x192.png",
            sizes: "192x192",
            type: "image/png",
          },
          {
            src: "pwa-512x512.png",
            sizes: "512x512",
            type: "image/png",
          },
          {
            src: "maskable-icon-512x512.png",
            sizes: "512x512",
            type: "image/png",
            purpose: "maskable",
          },
        ],
        screenshots: [
          {
            src: "screenshot.png",
            sizes: "770x598",
            type: "image/png",
            form_factor: "wide",
            label: "Sababuu App",
          },
          {
            src: "screenshot.png",
            sizes: "770x598",
            type: "image/png",
            form_factor: "narrow",
            label: "Sababuu App",
          },
        ],
      },
      devOptions: {
        enabled: process.env.VITE_APP_ENV !== "prod",
        type: "module",
      },
      registerType: "autoUpdate",
      injectRegister: "script",
      workbox: {
        globPatterns: ["**/*.{js,css,html,ico}"],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/cdn-icons-png\.freepik\.com\/.*/i,
            handler: "CacheFirst",
            options: {
              cacheName: "external-png-assets",
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
              },
              cacheableResponse: {
                statuses: [0, 200],
              },
            },
          },
        ],
      },
      includeAssets: [
        "src/assets/illustrations/preloaderSimple.png",
        "src/assets/icons/gift.png",
        "src/assets/icons/checkback-later.gif",
      ],
    }),
    svgLoader(),
  ],
  server: {
    host: true,
    port: 8000,
    watch: {
      usePolling: true,
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
