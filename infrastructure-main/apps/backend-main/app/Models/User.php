<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\User\ActionType;
use App\Enums\User\Gender;
use App\Enums\Profile\Verify\VerificationStatus;
use App\Events\NotificationsIndicatorsUpdated;
use App\Http\DataObjects\User\UserSettings;
use App\Traits\RedisAssistant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\Notification;
use Laravel\Sanctum\HasApiTokens;
use DateTime;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\Storage;
use NotificationChannels\WebPush\HasPushSubscriptions;

/**
 * @property int $id
 * @property string $name
 * @property string $phone Example: +254712345678
 * @property string $phone_correspondent
 * @property Gender $gender
 * @property Gender $oppositeGender
 * @property string $country
 * @property bool $is_boosted
 * @property string $description
 * @property string $birthday
 * @property string $location
 * @property string $latitude
 * @property string $longitude
 * @property string $avatar
 * @property int $distance
 * @property int $age
 * @property bool $isInCommunity
 * @property Profile $profile @deprecated
 * @property Wallet $wallet
 * @property UserMeta $userMeta
 * @property Image $mainImage
 * @property UserSettings|null $settings
 * @property Collection<int,Image> $gallery
 * @property Collection<int,Community> $communities
 * @property Collection<int,Image> $images
 * @property Collection<int,ProfileBoost> $boosts
 * @property Collection<int,VerificationRequest> $verificationRequests
 * @property Collection<int,Dialog> $allDialogsSender
 * @property Collection<int,Dialog> $allDialogsRecipient
 * @property Collection<int,Dialog> $allDialogs
 * @property Collection<int,Action> $receivedActions
 * @property Collection<int,User> $likedUsers
 * @property Collection<int,User> $dislikedUsers
 * @property OneSignalSubscription $oneSignalSubscription
 */
class User extends Authenticatable
{
    use HasApiTokens,
        HasFactory,
        Notifiable,
        RedisAssistant,
        SoftDeletes,
        HasPushSubscriptions;

    public const MIN_AGE = 18;
    public const MAX_AGE = 70;
    /**
     * @var array<int, string>
     */
    protected $fillable = [
        "phone",
        "phone_correspondent",
        "password",
        "gender",
        "country",
        "is_boosted",
        "group",
        "name",
        "description",
        "birthday",
        "location",
        "latitude",
        "longitude",
    ];

    /**
     * @var array<int, string>
     */
    protected $hidden = ["password", "remember_token"];

    /**
     * @var array<string, string>
     */
    protected $casts = [
        "gender" => Gender::class,
        "settings" => UserSettings::class,
    ];

    public $with = ['latestVerificationRequest'];

    protected static function boot(): void
    {
        static::deleting(function (self $user) {
            $user->images()->delete();
            $user->allDialogsSender()->delete();
            $user->allDialogsRecipient()->delete();
            $user->actions()->delete();
            $user->receivedActions()->delete();
            $user->verificationRequests()->delete();
            $user->tokens()->delete();
        });

        parent::boot();
    }

    public function markAsReadNotifications(
        array $types,
        ?int $dialogId = null
    ): void {
        $this->unreadNotifications()
            ->whereIn("type", $types)
            ->when($dialogId, function ($query) use ($dialogId) {
                $query->where("data->dialog_id", $dialogId);
            })
            ->get()
            ->markAsRead();

        NotificationsIndicatorsUpdated::dispatch($this);
    }

    public function createNotification(Notification $notification): void
    {
        $this->notify($notification);
    }

    /**
     * @deprecated
     * @return HasOne
     **/
    public function profile(): HasOne
    {
        return $this->hasOne(Profile::class);
    }

    public function wallet(): HasOne
    {
        return $this->hasOne(Wallet::class);
    }

    public function userMeta(): HasOne
    {
        return $this->hasOne(UserMeta::class);
    }

    public function boosts()
    {
        return $this->hasMany(BoostedProfile::class, "user_id");
    }

    public function actions(): HasMany
    {
        return $this->hasMany(Action::class, "user_id_sender");
    }

    public function receivedActions(): HasMany
    {
        return $this->hasMany(Action::class, "user_id_recipient");
    }

    public function likeActions(): HasMany
    {
        return $this->hasMany(Action::class, "user_id_sender")->where(
            "type",
            ActionType::LIKE
        );
    }

    public function dislikeActions(): HasMany
    {
        return $this->hasMany(Action::class, "user_id_sender")->where(
            "type",
            ActionType::DISLIKE
        );
    }

    public function usersThatLikedThis(): BelongsToMany
    {
        return $this->belongsToMany(
            __CLASS__,
            "actions",
            "user_id_recipient",
            "user_id_sender"
        )->whereIn("type", [ActionType::LIKE, ActionType::GIFT]);
    }

    public function likedUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            __CLASS__,
            "actions",
            "user_id_sender",
            "user_id_recipient"
        )->where("type", ActionType::LIKE);
    }

    public function dislikedUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            __CLASS__,
            "actions",
            "user_id_sender",
            "user_id_recipient"
        )->where("type", ActionType::DISLIKE);
    }

    public function getLikedUserByUserId(int $userId): User|null
    {
        return $this->likedUsers()
            ->where("user_id_recipient", $userId)
            ->first();
    }

    public function getDislikedUserByUserId(int $userId): User|null
    {
        return $this->dislikedUsers()
            ->where("user_id_recipient", $userId)
            ->first();
    }

    public function isLikedOrReportedUserById(int $userId): bool
    {
        return $this->actions()->where('user_id_recipient', $userId)->whereIn('type', [ActionType::LIKE, ActionType::REPORT])->exists();
    }

    public function gallery(): HasMany
    {
        return $this->hasMany(Image::class, "user_id")->where(
            "main",
            Image::IS_NOT_MAIN
        );
    }

    public function images(): HasMany
    {
        return $this->hasMany(Image::class, "user_id");
    }

    public function mainImage(): HasOne
    {
        return $this->hasOne(Image::class, "user_id")->where("main", Image::IS_MAIN);
    }

    protected function avatar(): Attribute
    {
        return Attribute::make(
            get: function () {
                $image = $this->mainImage?->publicUrl;
                return $image ?: Storage::url('/assets/deleted.svg');
            }
        );
    }

    public function verificationRequests(): HasMany
    {
        return $this->hasMany(VerificationRequest::class);
    }

    public function isVerified(): bool
    {
        return $this->verificationStatus() ===
            VerificationStatus::APPROVED->value;
    }

    public function latestVerificationRequest(): HasOne
    {
        return $this->hasOne(VerificationRequest::class)->orderByDesc('created_at');
    }

    public function isFromGhana(): bool
    {
        return $this->country === "ghana";
    }

    public function verificationStatus(): string|null
    {
        return $this->latestVerificationRequest?->status;
    }

    public function verificationRequested(): bool
    {
        return $this->latestVerificationRequest?->status === VerificationStatus::PENDING;
    }

    public function updateLastSeenAt(): self
    {
        $timestamp = now()->toDateTimeString();
        $this->last_seen_at = $timestamp;
        $this->save();
        $this->userMeta()->update([
            "last_seen_at" => $timestamp,
        ]);
        return $this;
    }

    public function subscribedToPushNotifications(): bool
    {
        return $this->pushSubscriptions()->exists();
    }

    public function properties(): BelongsToMany
    {
        return $this->belongsToMany(Property::class, 'users_properties', 'user_id', 'property_id')
            ->using(UserProperty::class)
            ->with(['category', 'enumValues'])
            ->withPivot(['value_text', 'value_number', 'property_enum_value_id'])
            ->withTimestamps();
    }

    public function chosenProperties(): HasMany
    {
        return $this->hasMany(UserProperty::class)->with(['property', 'property.enumValues', 'property.category']);
    }

    public function oneSignalSubscription(): HasOne
    {
        return $this->hasOne(OneSignalSubscription::class);
    }

    public function routeNotificationForOneSignal()
    {
        return $this->oneSignalSubscription()->get()->first()->subscription_id;
    }

    public function allDialogsRecipient(): HasMany
    {
        return $this->hasMany(Dialog::class, "user_id_recipient");
    }

    public function allDialogsSender(): HasMany
    {
        return $this->hasMany(Dialog::class, "user_id_sender");
    }

    public function allDialogs(): HasMany
    {
        return $this->allDialogsRecipient()->union($this->allDialogsSender());
    }

    public function communities(): BelongsToMany
    {
        return $this->belongsToMany(
            Community::class,
            "community_members",
            "user_id",
            "community_id"
        )->whereNull("community_members.deleted_at");
    }

    /**
     * Update the is_boosted field for the profile.
     *
     * @param bool $value The value to set for is_boosted (true/false).
     * @return bool
     */
    public function updateIsBoosted(bool $value): bool
    {
        $this->is_boosted = $value; // Update the property
        return $this->save(); // Save the changes to the database
    }

    /**
     * The channels the user receives notification broadcasts on.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return "User." . $this->id;
    }

    protected function distanceToGuestUser(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (auth()->check()) {
                    $currentUser = user();

                    $lat1 = floatval($currentUser->latitude);
                    $lon1 = floatval($currentUser->longitude);
                    $lat2 = floatval($this->attributes["latitude"]);
                    $lon2 = floatval($this->attributes["longitude"]);

                    if ($lat1 && $lon1 && $lat2 && $lon2) {
                        $theta = $lon1 - $lon2;
                        $dist =
                            sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +
                            cos(deg2rad($lat1)) *
                            cos(deg2rad($lat2)) *
                            cos(deg2rad($theta));
                        $dist = acos($dist);
                        $dist = rad2deg($dist);
                        $miles = $dist * 60 * 1.1515;

                        // Convert miles to kilometers
                        $kilometers = $miles * 1.609344;

                        $formattedDistance = number_format($kilometers);
                        $formattedDistance =
                            $formattedDistance < 1 ? "<1" : $formattedDistance;

                        return $formattedDistance;
                    }
                }

                return null;
            }
        );
    }


    public function compliment(): MorphOne
    {
        return $this->morphOne(Compliment::class, 'actionable');
    }

    protected function age(): Attribute
    {
        return Attribute::make(
            get: fn() => now()->diffInYears($this->birthday),
        )->shouldCache();
    }

    public function oppositeGender(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->gender === Gender::MALE ? Gender::FEMALE : Gender::MALE,
        )->shouldCache();
    }

    public function isInCommunity(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->communities()->exists(),
        );
    }
}
