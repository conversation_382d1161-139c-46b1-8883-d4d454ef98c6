<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\User\ActionType;
use App\Enums\Profile\Verify\VerificationStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

/**
 * @deprecated
 */
class Profile extends Model
{
    use HasFactory, SoftDeletes;

    public const MIN_AGE = 18;
    public const MAX_AGE = 70;

    protected $fillable = [
        'name',
        'birthday',
        'location',
        'description',
        'verification',
        'latitude',
        'longitude',
        'is_boosted',
    ];

    protected static function boot(): void
    {
        static::deleting(function (self $profile) {
            $profile->images()->delete();
            $profile->allDialogsSender()->delete();
            $profile->allDialogsRecipient()->delete();
            $profile->actions()->delete();
            $profile->receivedActions()->delete();
            $profile->verificationRequests()->delete();
        });

        parent::boot();
    }

    // public function actions(): HasMany
    // {
    //     return $this->hasMany(Action::class, 'who_profile_id');
    // }

    // public function receivedActions(): HasMany
    // {
    //     return $this->hasMany(Action::class, 'whom_profile_id');
    // }

    // public function likeActions(): HasMany
    // {
    //     return $this->hasMany(Action::class, 'who_profile_id')->where('type', ActionType::LIKE);
    // }

    // public function dislikeActions(): HasMany
    // {
    //     return $this->hasMany(Action::class, 'who_profile_id')->where('type', ActionType::DISLIKE);
    // }

    // public function profilesThatLikedThis(): BelongsToMany
    // {
    //     return $this->belongsToMany(__CLASS__, 'actions', 'whom_profile_id', 'who_profile_id')
    //         ->whereIn('type', [ActionType::LIKE, ActionType::GIFT]);
    // }

    // public function likedProfiles(): BelongsToMany
    // {
    //     return $this->belongsToMany(__CLASS__, 'actions', 'who_profile_id', 'whom_profile_id')
    //         ->where('type', ActionType::LIKE);
    // }

    // /**
    //  * @return ?Model<Action>
    //  */
    // public function likeByIdWho(int $whoProfileId): ?Model
    // {
    //     return $this->hasMany(Action::class, 'whom_profile_id', 'id')
    //         ->where('type', ActionType::LIKE)
    //         ->where('who_profile_id', $whoProfileId)
    //         ->first();
    // }

    // /**
    //  * @return Model<Action>
    //  */
    // public function dislikeByIdWho(int $whoProfileId): Model
    // {
    //     return $this->hasMany(Action::class, 'whom_profile_id', 'id')
    //         ->where('type', ActionType::DISLIKE)
    //         ->where('who_profile_id', $whoProfileId)
    //         ->first();
    // }

    // public function getProfileLikedByProfileId(int $profileId): Profile|null
    // {
    //     return $this->likedProfiles()->where('whom_profile_id', $profileId)->first();
    // }

    // public function getProfileDislikedByProfileId(int $profileId): Profile|null
    // {
    //     return $this->dislikes()->where('whom_profile_id', $profileId)->first();
    // }

    public function dislikes(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'actions', 'who_profile_id', 'whom_profile_id')
            ->where('type', ActionType::DISLIKE);
    }

    public function whoDislikes(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'actions', 'whom_profile_id', 'who_profile_id')
            ->where('type', ActionType::DISLIKE);
    }

    public function actionsWho(): HasMany
    {
        return $this->hasMany(
            Action::class,
            'who_profile_id'
        );
    }
    public function actionsWhom(): HasMany
    {
        return $this->hasMany(
            Action::class,
            'whom_profile_id'
        );
    }

    public function actionByAuthWhomId()
    {
        $authUserId = Auth::user()->profile->id;
        return Action::query()->where('whom_profile_id', $authUserId)
            ->where('who_profile_id', $this->id)
            ->where('type', ActionType::LIKE)
            ->first();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // public function wallet(): HasOne
    // {
    //     return $this->hasOne(Wallet::class);
    // }

    public function oneSignalSubscription(): HasOne
    {
        return $this->hasOne(OneSignalSubscription::class);
    }

    public function allDialogsRecipient(): HasMany
    {
        return $this->hasMany(Dialog::class, 'profile_id_recipient');
    }

    public function allDialogsSender(): HasMany
    {
        return $this->hasMany(Dialog::class, 'profile_id_sender');
    }
    public function boosts()
    {
        return $this->hasMany(BoostedProfile::class, 'user_id', 'user_id');
    }

    // public function gallery(): HasMany
    // {
    //     return $this->hasMany(Image::class, 'profile_id')->where('main', Image::IS_NOT_MAIN);
    // }

    // public function images(): HasMany
    // {
    //     return $this->hasMany(Image::class, 'profile_id');
    // }

    public function communities(): BelongsToMany
    {
        return $this->belongsToMany(Community::class, 'community_members', 'profile_id', 'community_id')->whereNull('community_members.deleted_at');
    }

    public function verificationRequests(): HasMany
    {
        return $this->hasMany(VerificationRequest::class, 'profile_id');
    }

    public function isVerified(): bool
    {
        $latestVerification = $this->verificationRequests()->orderByDesc('created_at')->first();
        if (!$latestVerification) return false;
        return $latestVerification->status === VerificationStatus::APPROVED->value;
    }

    public function getAvatarAttribute(): ?string
    {
        if ($image = $this->images()->where('main', Image::IS_MAIN)?->first()?->publicUrl) {
            return $image;
        } else {
            return Storage::url('/assets/deleted.svg');
        }
    }

    public function getProfileDistanceAttribute()
    {
        if (auth()->check()) {
            $userProfile = Auth::user()->profile;

            $lat1 = floatval($userProfile->latitude);
            $lon1 = floatval($userProfile->longitude);
            $lat2 = floatval($this->attributes['latitude']);
            $lon2 = floatval($this->attributes['longitude']);

            if ($lat1 && $lon1 && $lat2 && $lon2) {
                $theta = $lon1 - $lon2;
                $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
                $dist = acos($dist);
                $dist = rad2deg($dist);
                $miles = $dist * 60 * 1.1515;

                // Convert miles to kilometers
                $kilometers = $miles * 1.609344;

                $formattedDistance = number_format($kilometers);
                $formattedDistance = $formattedDistance < 1 ? '<1' : $formattedDistance;

                return $formattedDistance;
            }
        }

        return null;
    }

    /**
     * Update the is_boosted field for the profile.
     *
     * @param bool $value The value to set for is_boosted (true/false).
     * @return bool
     */
    public function updateIsBoosted(bool $value): bool
    {
        $this->is_boosted = $value; // Update the property
        return $this->save();       // Save the changes to the database
    }

    public function updateProfileMetaLastSeenAt(String $timestamp): void
    {
        $this->profileMeta()->update([
            "last_seen_at" => $timestamp
        ]);
    }

    public function gifts()
    {
        return $this->belongsToMany(Gift::class, 'user_gifts')->using(UserGift::class);
    }
}
