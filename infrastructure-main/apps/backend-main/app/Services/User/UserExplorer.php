<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Enums\User\ActionType;
use App\Exceptions\User\NoMoreUsersException;
use App\Models\Action;
use App\Models\User;
use App\Models\UserProperty;
use App\Repositories\User\UserRepository;
use App\Services\ExternalAPI\Pusher\PresenceChannel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class UserExplorer
{
    public const AGE_BETWEEN = 3;
    public const ONLINE_USERS_PAGE_SIZE = 8;
    public const WHO_LIKED_ME_PAGE_SIZE = 8;

    private ?User $user;

    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly PresenceChannel $presenceChannel,
        private readonly UserSettingsService $userSettingsService
    ) {}

    public function setUser(User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function getBunchWithActionCountGroupCache($oldIds = []): Collection
    {
        $users = $this->getCombinedUsers($oldIds);
        if ($users->isEmpty()) {
            $this->userSettingsService->adjustAge();
            $this->userSettingsService->adjustDistance();
            $users = $this->getCombinedUsers($oldIds);
        }
        if ($liker = $this->getMyLikerForToday()) {
            $users->push($liker);
        }
        if ($users->isEmpty()) {
            throw new NoMoreUsersException();
        }
        $users = $this->manualBoostedUserPlacement($users);
        return $users;
    }

    protected function getCombinedUsers($oldIds = []): Collection
    {
        $users = $this->userRepository->usersToExplore($this->user, $oldIds);
        $boostedUsers = $this->userRepository->boostedUsersToExplore($this->user, $oldIds);

        return $users->concat($boostedUsers)->shuffle();
    }

    /**
     * This function manually places the first boosted user in the combinedUsers collection
     * at a random position, ensuring that the boosted user is not placed next to another
     * boosted user.
     *
     * @param Builder|Collection $combinedUsers The collection of users to be processed.
     * @return Builder|Collection The collection with the first boosted user placed at a valid position.
     */
    private function manualBoostedUserPlacement(Builder|Collection $combinedUsers): Builder|Collection
    {
        if ($combinedUsers->isEmpty() || $combinedUsers->count() < 5) {
            return $combinedUsers;
        }

        if ($combinedUsers->first()?->is_boosted) {
            $firstBoosted = $combinedUsers->shift();
            $validIndex = null;

            // Find a valid index where the boosted user can be placed without being next to another boosted user
            while ($validIndex === null && $combinedUsers->count() > 1) {
                $randomIndex = rand(1, $combinedUsers->count() - 1);

                // Check if the user before and after the random index are boosted
                $beforeIsBoosted = $randomIndex > 0 && optional($combinedUsers->get($randomIndex - 1))->is_boosted;
                $afterIsBoosted = $randomIndex < $combinedUsers->count() && optional($combinedUsers->get($randomIndex))->is_boosted;

                // If neither the before nor after user is boosted, we found a valid index
                if (!$beforeIsBoosted && !$afterIsBoosted) {
                    $validIndex = $randomIndex;
                }
            }

            // If a valid index was found, insert the first boosted user at that index
            if ($validIndex !== null) {
                $combinedUsers->splice($validIndex, 0, [$firstBoosted]);
            }
        }

        return $combinedUsers;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Collection<User>
     */
    public function getOnlineUsers(): Collection
    {
        $users = $this->presenceChannel->getUsers('presence-Users');
        $userIds = (new Collection($users))->pluck('id')->toArray();

        $usersQuery = User::query()
            ->where(fn(Builder $query) => $query->whereIn('id', $userIds)->orWhere('last_seen_at', '>', Carbon::now()->subMinutes(30)))
            ->whereNot('id', $this->user->id)
            ->whereNotNull('description') // users without description are not completely registered
            ->where('country', $this->user->country)
            ->where('gender', $this->user->oppositeGender)
            ->orderBy(
                Action::query()
                    ->select('id')
                    ->where('user_id_sender', $this->user->id)
                    ->whereColumn('actions.user_id_recipient', 'users.id')
                    ->where('type', ActionType::LIKE)
                    ->take(1),
                'desc'
            )
            ->orderBy('users.last_seen_at', 'desc')
            ->with([
                'gallery',
                'mainImage',
                'receivedActions' => function (HasMany $q) {
                    return $q->select(['actions.type', 'actions.id', 'actions.created_at', 'actions.user_id_recipient'])->where("user_id_sender", $this->user->id)->orderBy('created_at', 'desc');
                },
                'allDialogs' => function (HasMany $q) {
                    return $q->where("user_id_sender", $this->user->id)->orWhere('user_id_recipient', $this->user->id);
                }
            ])
            ->limit(100);
        $userQuery2 = clone $usersQuery; // cloning query before adding filters to add adjusted one's once we don't have any users
        $this->userRepository->addFilters($usersQuery);

        $users = $usersQuery->get();
        if ($users->isEmpty()) {
            $this->userSettingsService->adjustAge();
            $this->userSettingsService->adjustDistance();
            $users = $this->userRepository->addFilters($userQuery2)->get();
        }
        return $users;
    }

    public function getFeedPaginator(bool $withoutFilters = false): LengthAwarePaginator
    {
        $likesQuery = Action::query()
            ->whereHas(
                'recipient',
                fn(Builder $q) => $q->where('users.id', $this->user->id)
            )
            ->whereNot(fn(Builder $q) => $q->where('user_id_sender', $this->user->id)->whereIn('type', [ActionType::LIKE, ActionType::REPORT]))
            ->whereDoesntHave('recipient.allDialogsRecipient', fn(Builder $q) => $q->where('dialogs.user_id_sender', DB::raw('`actions`.`user_id_sender`')))
            ->whereDoesntHave('recipient.allDialogsSender', fn(Builder $q) => $q->where('dialogs.user_id_recipient', DB::raw('`actions`.`user_id_sender`')))
            ->whereIn('type', [ActionType::LIKE, ActionType::GIFT])
            ->orderBy('actions.created_at', 'desc')
            ->with([
                'sender',
                'revealed',
                'gift',
                'compliment.actionable' => function (MorphTo $morphTo) {
                    $morphTo->morphWith([
                        UserProperty::class => ['property.category', 'enumValue']
                    ]);
                },
            ]);

        $likesQuery2 = clone $likesQuery; // cloning query before adding filters to add adjusted one's once we don't have any users
        if (!$withoutFilters) {
            $likesQuery->whereHas('sender', function (Builder $q) {
                $this->userRepository->addFilters($q);
            });
            if ($likesQuery->count() === 0) {
                $this->userSettingsService->adjustAge();
                $this->userSettingsService->adjustDistance();
                $likesQuery2->whereHas('sender', function (Builder $q) {
                    $this->userRepository->addFilters($q);
                });
                return $likesQuery2->paginate(self::WHO_LIKED_ME_PAGE_SIZE);
            }
        }
        return $likesQuery->paginate(self::WHO_LIKED_ME_PAGE_SIZE);
    }

    public function augmentIsDislikedFlag(SupportCollection $likes): SupportCollection
    {
        // Get all sender user IDs from feed entries
        $senderIds = $likes->pluck('user_id_sender')->all();
        // Get IDs of users disliked by the current user among the senders
        $dislikedIds = $this->user
            ->dislikedUsers()
            ->whereIn('users.id', $senderIds)
            ->pluck('users.id')
            ->all();

        $withIsDislikedFlag = $likes->map(function ($item) use ($dislikedIds) {
            $item->is_disliked = in_array($item->user_id_sender, $dislikedIds);
            return $item;
        });

        return $withIsDislikedFlag;
    }

    /**
     * Get boosted users for discovery
     *
     * @param array $filters
     * @return Collection
     */
    public function exploreBoostedUsers(array $filters = []): Collection
    {
        $oldIds = $filters['old_id'] ?? [];
        return $this->userRepository->boostedUsersToExplore($this->user, $oldIds);
    }

    public function getMyLikerForToday(): User|null
    {
        $user = user();
        $likersReturnCount = (int)Redis::get("likers-returned:{$user->id}");
        // Check if the user has already returned 2 or more times during the last 24 hours
        if ($likersReturnCount >= 2) {
            return null;
        } else {
            $liker = $user->usersThatLikedThis()
                ->whereDoesntHave('allDialogsSender', function (Builder $q) use ($user) {
                    $q->where('dialogs.user_id_recipient', $user->id);
                })
                ->whereDoesntHave('allDialogsRecipient', function (Builder $q) use ($user) {
                    $q->where('dialogs.user_id_sender', $user->id);
                })
                ->with([
                    'mainImage',
                    'gallery',
                    'chosenProperties',
                    'boosts'
                ])
                ->orderBy('actions.created_at', 'desc')
                ->first();
            Redis::set("likers-returned:{$user->id}", ++$likersReturnCount, 'EX', 60 * 60 * 24);
            return $liker;
        }
    }
}
